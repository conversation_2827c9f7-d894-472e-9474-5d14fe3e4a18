//+------------------------------------------------------------------+
//|                                                BaseComponent.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef BASE_COMPONENT_MQH
#define BASE_COMPONENT_MQH

//+------------------------------------------------------------------+
//| BaseComponent Class                                              |
//| Base class providing common functionality for all components    |
//| in the EA_Wizard framework                                       |
//+------------------------------------------------------------------+
class BaseComponent
{
private:
    string            m_name;              // Component name
    bool              m_initialized;       // Initialization status
    bool              m_enabled;           // Component enabled status
    datetime          m_lastUpdate;        // Last update timestamp
    int               m_errorCode;         // Last error code
    string            m_errorMessage;      // Last error message

protected:
    //--- Protected methods for derived classes
    void              SetError(int code, string message);
    void              ClearError();
    bool              ValidateParameters();
    
public:
    //--- Constructor and Destructor
                      BaseComponent(string name = "BaseComponent");
    virtual          ~BaseComponent();
    
    //--- Initialization lifecycle methods
    virtual bool      Initialize();
    virtual bool      Validate();
    virtual void      Reset();
    virtual void      Cleanup();
    
    //--- Status and information methods
    string            GetName() const { return m_name; }
    bool              IsInitialized() const { return m_initialized; }
    bool              IsEnabled() const { return m_enabled; }
    datetime          GetLastUpdate() const { return m_lastUpdate; }
    
    //--- Error handling methods
    int               GetLastError() const { return m_errorCode; }
    string            GetLastErrorMessage() const { return m_errorMessage; }
    bool              HasError() const { return m_errorCode != 0; }
    
    //--- Control methods
    void              Enable() { m_enabled = true; }
    void              Disable() { m_enabled = false; }
    void              SetEnabled(bool enabled) { m_enabled = enabled; }
    
    //--- Update methods
    virtual bool      Update();
    void              UpdateTimestamp() { m_lastUpdate = TimeCurrent(); }
    
    //--- Virtual methods to be implemented by derived classes
    virtual bool      OnInitialize() { return true; }
    virtual bool      OnValidate() { return true; }
    virtual void      OnReset() {}
    virtual void      OnCleanup() {}
    virtual bool      OnUpdate() { return true; }
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
BaseComponent::BaseComponent(string name = "BaseComponent")
{
    m_name = name;
    m_initialized = false;
    m_enabled = true;
    m_lastUpdate = 0;
    m_errorCode = 0;
    m_errorMessage = "";
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
BaseComponent::~BaseComponent()
{
    Cleanup();
}

//+------------------------------------------------------------------+
//| Initialize component                                             |
//+------------------------------------------------------------------+
bool BaseComponent::Initialize()
{
    ClearError();
    
    if (m_initialized)
    {
        SetError(1, "Component already initialized");
        return false;
    }
    
    if (!ValidateParameters())
    {
        SetError(2, "Parameter validation failed");
        return false;
    }
    
    if (!OnInitialize())
    {
        SetError(3, "Component initialization failed");
        return false;
    }
    
    m_initialized = true;
    UpdateTimestamp();
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate component parameters                                    |
//+------------------------------------------------------------------+
bool BaseComponent::Validate()
{
    ClearError();
    
    if (!OnValidate())
    {
        SetError(4, "Component validation failed");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Reset component to initial state                                |
//+------------------------------------------------------------------+
void BaseComponent::Reset()
{
    ClearError();
    m_initialized = false;
    m_lastUpdate = 0;
    OnReset();
}

//+------------------------------------------------------------------+
//| Cleanup component resources                                     |
//+------------------------------------------------------------------+
void BaseComponent::Cleanup()
{
    OnCleanup();
    m_initialized = false;
    m_enabled = false;
}

//+------------------------------------------------------------------+
//| Update component                                                 |
//+------------------------------------------------------------------+
bool BaseComponent::Update()
{
    if (!m_initialized || !m_enabled)
    {
        return false;
    }
    
    ClearError();
    
    if (!OnUpdate())
    {
        SetError(5, "Component update failed");
        return false;
    }
    
    UpdateTimestamp();
    return true;
}

//+------------------------------------------------------------------+
//| Set error information                                            |
//+------------------------------------------------------------------+
void BaseComponent::SetError(int code, string message)
{
    m_errorCode = code;
    m_errorMessage = message;
    Print("ERROR [", m_name, "]: ", message, " (Code: ", code, ")");
}

//+------------------------------------------------------------------+
//| Clear error information                                          |
//+------------------------------------------------------------------+
void BaseComponent::ClearError()
{
    m_errorCode = 0;
    m_errorMessage = "";
}

//+------------------------------------------------------------------+
//| Validate parameters (base implementation)                       |
//+------------------------------------------------------------------+
bool BaseComponent::ValidateParameters()
{
    // Base implementation - always returns true
    // Derived classes should override this method
    return true;
}

#endif // BASE_COMPONENT_MQH
