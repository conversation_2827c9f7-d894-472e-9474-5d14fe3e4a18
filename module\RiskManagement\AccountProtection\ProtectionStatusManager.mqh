//+------------------------------------------------------------------+
//|                                    ProtectionStatusManager.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef PROTECTION_STATUS_MANAGER_MQH
#define PROTECTION_STATUS_MANAGER_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"
#include "AccountProtectionConfig.mqh"
#include "AccountMonitor.mqh"

//+------------------------------------------------------------------+
//| ProtectionStatusManager Class                                    |
//| Responsible for managing protection status and state changes    |
//| Single Responsibility: Status Management                        |
//+------------------------------------------------------------------+
class ProtectionStatusManager : public BaseComponent
{
private:
    AccountProtectionConfig* m_config;              // Configuration component
    AccountMonitor*         m_monitor;              // Monitor component
    
    ENUM_PROTECTION_STATUS  m_currentStatus;        // Current protection status
    ENUM_PROTECTION_STATUS  m_previousStatus;       // Previous protection status
    datetime                m_statusChangeTime;     // Last status change time
    string                  m_statusMessage;        // Status message
    
    // Status thresholds (as percentage of limits)
    double                  m_warningThreshold;     // Warning threshold (50%)
    double                  m_criticalThreshold;    // Critical threshold (70%)
    double                  m_emergencyThreshold;   // Emergency threshold (90%)
    
public:
    //--- Constructor and Destructor
                            ProtectionStatusManager(AccountProtectionConfig* config, 
                                                  AccountMonitor* monitor);
    virtual                ~ProtectionStatusManager();
    
    //--- Status management methods
    void                    UpdateStatus();
    void                    SetStatus(ENUM_PROTECTION_STATUS status, string message = "");
    void                    ResetStatus();
    
    //--- Information methods
    ENUM_PROTECTION_STATUS  GetCurrentStatus() const { return m_currentStatus; }
    ENUM_PROTECTION_STATUS  GetPreviousStatus() const { return m_previousStatus; }
    datetime                GetStatusChangeTime() const { return m_statusChangeTime; }
    string                  GetStatusMessage() const { return m_statusMessage; }
    string                  GetStatusDescription() const;
    
    //--- Status check methods
    bool                    IsNormal() const { return m_currentStatus == STATUS_NORMAL; }
    bool                    IsWarning() const { return m_currentStatus == STATUS_WARNING; }
    bool                    IsCritical() const { return m_currentStatus == STATUS_CRITICAL; }
    bool                    IsEmergency() const { return m_currentStatus == STATUS_EMERGENCY; }
    bool                    HasStatusChanged() const;
    
    //--- Threshold configuration methods
    void                    SetWarningThreshold(double threshold);
    void                    SetCriticalThreshold(double threshold);
    void                    SetEmergencyThreshold(double threshold);
    double                  GetWarningThreshold() const { return m_warningThreshold; }
    double                  GetCriticalThreshold() const { return m_criticalThreshold; }
    double                  GetEmergencyThreshold() const { return m_emergencyThreshold; }
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    string                  GetStatusSummary() const;
    void                    LogStatusChange() const;
    
private:
    //--- Internal methods
    ENUM_PROTECTION_STATUS  CalculateStatus();
    bool                    IsLossThresholdExceeded(double threshold);
    bool                    IsDrawdownThresholdExceeded(double threshold);
    string                  GenerateStatusMessage(ENUM_PROTECTION_STATUS status);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
ProtectionStatusManager::ProtectionStatusManager(AccountProtectionConfig* config, 
                                                AccountMonitor* monitor) : BaseComponent("ProtectionStatusManager")
{
    m_config = config;
    m_monitor = monitor;
    
    m_currentStatus = STATUS_NORMAL;
    m_previousStatus = STATUS_NORMAL;
    m_statusChangeTime = 0;
    m_statusMessage = "";
    
    // Set default thresholds
    m_warningThreshold = 50.0;      // 50% of limit
    m_criticalThreshold = 70.0;     // 70% of limit
    m_emergencyThreshold = 90.0;    // 90% of limit
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
ProtectionStatusManager::~ProtectionStatusManager()
{
    // Components are managed externally
}

//+------------------------------------------------------------------+
//| Initialize status manager                                        |
//+------------------------------------------------------------------+
bool ProtectionStatusManager::OnInitialize()
{
    if (m_config == NULL)
    {
        SetError(5001, "Configuration component not set");
        return false;
    }
    
    if (m_monitor == NULL)
    {
        SetError(5002, "Monitor component not set");
        return false;
    }
    
    if (!m_config.IsInitialized() || !m_monitor.IsInitialized())
    {
        SetError(5003, "One or more components not initialized");
        return false;
    }
    
    m_currentStatus = STATUS_NORMAL;
    m_previousStatus = STATUS_NORMAL;
    m_statusChangeTime = TimeCurrent();
    m_statusMessage = "Protection status manager initialized";
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate status manager                                          |
//+------------------------------------------------------------------+
bool ProtectionStatusManager::OnValidate()
{
    if (m_config == NULL || m_monitor == NULL)
    {
        SetError(5004, "Component validation failed");
        return false;
    }
    
    if (m_warningThreshold >= m_criticalThreshold || 
        m_criticalThreshold >= m_emergencyThreshold)
    {
        SetError(5005, "Invalid threshold configuration");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update status manager                                            |
//+------------------------------------------------------------------+
bool ProtectionStatusManager::OnUpdate()
{
    UpdateStatus();
    return true;
}

//+------------------------------------------------------------------+
//| Update protection status                                         |
//+------------------------------------------------------------------+
void ProtectionStatusManager::UpdateStatus()
{
    ENUM_PROTECTION_STATUS newStatus = CalculateStatus();
    
    if (newStatus != m_currentStatus)
    {
        m_previousStatus = m_currentStatus;
        m_currentStatus = newStatus;
        m_statusChangeTime = TimeCurrent();
        m_statusMessage = GenerateStatusMessage(newStatus);
        
        LogStatusChange();
    }
}

//+------------------------------------------------------------------+
//| Set protection status manually                                   |
//+------------------------------------------------------------------+
void ProtectionStatusManager::SetStatus(ENUM_PROTECTION_STATUS status, string message = "")
{
    if (status != m_currentStatus)
    {
        m_previousStatus = m_currentStatus;
        m_currentStatus = status;
        m_statusChangeTime = TimeCurrent();
        m_statusMessage = (message != "") ? message : GenerateStatusMessage(status);
        
        LogStatusChange();
    }
}

//+------------------------------------------------------------------+
//| Reset status to normal                                           |
//+------------------------------------------------------------------+
void ProtectionStatusManager::ResetStatus()
{
    SetStatus(STATUS_NORMAL, "Status reset to normal");
}

//+------------------------------------------------------------------+
//| Get status description                                           |
//+------------------------------------------------------------------+
string ProtectionStatusManager::GetStatusDescription() const
{
    switch(m_currentStatus)
    {
        case STATUS_NORMAL:     return "Normal";
        case STATUS_WARNING:    return "Warning";
        case STATUS_CRITICAL:   return "Critical";
        case STATUS_EMERGENCY:  return "Emergency";
        default:                return "Unknown";
    }
}

//+------------------------------------------------------------------+
//| Check if status has changed                                      |
//+------------------------------------------------------------------+
bool ProtectionStatusManager::HasStatusChanged() const
{
    return (m_currentStatus != m_previousStatus);
}

//+------------------------------------------------------------------+
//| Set warning threshold                                            |
//+------------------------------------------------------------------+
void ProtectionStatusManager::SetWarningThreshold(double threshold)
{
    m_warningThreshold = MathMax(10.0, MathMin(90.0, threshold));
}

//+------------------------------------------------------------------+
//| Set critical threshold                                           |
//+------------------------------------------------------------------+
void ProtectionStatusManager::SetCriticalThreshold(double threshold)
{
    m_criticalThreshold = MathMax(20.0, MathMin(95.0, threshold));
}

//+------------------------------------------------------------------+
//| Set emergency threshold                                          |
//+------------------------------------------------------------------+
void ProtectionStatusManager::SetEmergencyThreshold(double threshold)
{
    m_emergencyThreshold = MathMax(30.0, MathMin(99.0, threshold));
}

//+------------------------------------------------------------------+
//| Get status summary                                               |
//+------------------------------------------------------------------+
string ProtectionStatusManager::GetStatusSummary() const
{
    string summary = "Protection Status: " + GetStatusDescription();
    summary += "\nPrevious: " + 
               ((m_previousStatus == STATUS_NORMAL) ? "Normal" :
                (m_previousStatus == STATUS_WARNING) ? "Warning" :
                (m_previousStatus == STATUS_CRITICAL) ? "Critical" : "Emergency");
    summary += "\nChanged: " + TimeToString(m_statusChangeTime);
    summary += "\nMessage: " + m_statusMessage;
    summary += StringFormat("\nThresholds: Warning=%.0f%%, Critical=%.0f%%, Emergency=%.0f%%",
                           m_warningThreshold, m_criticalThreshold, m_emergencyThreshold);
    
    return summary;
}

//+------------------------------------------------------------------+
//| Log status change                                                |
//+------------------------------------------------------------------+
void ProtectionStatusManager::LogStatusChange() const
{
    Print("PROTECTION STATUS CHANGE: ", GetStatusDescription());
    Print("  Previous: ", (m_previousStatus == STATUS_NORMAL) ? "Normal" :
                         (m_previousStatus == STATUS_WARNING) ? "Warning" :
                         (m_previousStatus == STATUS_CRITICAL) ? "Critical" : "Emergency");
    Print("  Message: ", m_statusMessage);
    Print("  Time: ", TimeToString(m_statusChangeTime));
}

//+------------------------------------------------------------------+
//| Calculate current status based on metrics                       |
//+------------------------------------------------------------------+
ENUM_PROTECTION_STATUS ProtectionStatusManager::CalculateStatus()
{
    if (m_config == NULL || m_monitor == NULL)
        return STATUS_NORMAL;
    
    // Check emergency threshold first
    if (IsLossThresholdExceeded(m_emergencyThreshold) || 
        IsDrawdownThresholdExceeded(m_emergencyThreshold))
    {
        return STATUS_EMERGENCY;
    }
    
    // Check critical threshold
    if (IsLossThresholdExceeded(m_criticalThreshold) || 
        IsDrawdownThresholdExceeded(m_criticalThreshold))
    {
        return STATUS_CRITICAL;
    }
    
    // Check warning threshold
    if (IsLossThresholdExceeded(m_warningThreshold) || 
        IsDrawdownThresholdExceeded(m_warningThreshold))
    {
        return STATUS_WARNING;
    }
    
    return STATUS_NORMAL;
}

//+------------------------------------------------------------------+
//| Check if loss threshold is exceeded                              |
//+------------------------------------------------------------------+
bool ProtectionStatusManager::IsLossThresholdExceeded(double threshold)
{
    if (m_config == NULL || m_monitor == NULL)
        return false;
    
    double currentLossPercent = m_monitor.GetCurrentLossPercent();
    double maxLossPercent = m_config.GetMaxLossPercent();
    double thresholdValue = maxLossPercent * (threshold / 100.0);
    
    return (currentLossPercent >= thresholdValue);
}

//+------------------------------------------------------------------+
//| Check if drawdown threshold is exceeded                          |
//+------------------------------------------------------------------+
bool ProtectionStatusManager::IsDrawdownThresholdExceeded(double threshold)
{
    if (m_config == NULL || m_monitor == NULL)
        return false;
    
    double currentDrawdownPercent = m_monitor.GetCurrentDrawdownPercent();
    double maxDrawdownPercent = m_config.GetMaxDrawdownPercent();
    double thresholdValue = maxDrawdownPercent * (threshold / 100.0);
    
    return (currentDrawdownPercent >= thresholdValue);
}

//+------------------------------------------------------------------+
//| Generate status message                                          |
//+------------------------------------------------------------------+
string ProtectionStatusManager::GenerateStatusMessage(ENUM_PROTECTION_STATUS status)
{
    if (m_config == NULL || m_monitor == NULL)
        return "Status updated";
    
    double lossPercent = m_monitor.GetCurrentLossPercent();
    double drawdownPercent = m_monitor.GetCurrentDrawdownPercent();
    
    switch(status)
    {
        case STATUS_NORMAL:
            return "All metrics within normal range";
            
        case STATUS_WARNING:
            return StringFormat("Warning: Loss=%.2f%%, Drawdown=%.2f%%", 
                               lossPercent, drawdownPercent);
            
        case STATUS_CRITICAL:
            return StringFormat("Critical: Loss=%.2f%%, Drawdown=%.2f%%", 
                               lossPercent, drawdownPercent);
            
        case STATUS_EMERGENCY:
            return StringFormat("EMERGENCY: Loss=%.2f%%, Drawdown=%.2f%%", 
                               lossPercent, drawdownPercent);
            
        default:
            return "Unknown status";
    }
}

#endif // PROTECTION_STATUS_MANAGER_MQH
