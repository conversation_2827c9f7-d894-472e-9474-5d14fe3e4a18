//+------------------------------------------------------------------+
//|                                              Example_Usage.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef EXAMPLE_USAGE_MQH
#define EXAMPLE_USAGE_MQH

#include "AccountProtection.mqh"

//+------------------------------------------------------------------+
//| Example: Basic Usage of Refactored AccountProtection            |
//+------------------------------------------------------------------+
void Example<PERSON>asicUsage()
{
    Print("=== AccountProtection Refactored Example ===");
    
    // Create AccountProtection with moderate protection level
    AccountProtection* protection = new AccountProtection(PROTECTION_MODERATE, true, Symbol());
    
    // Initialize the protection system
    if (!protection.Initialize())
    {
        Print("Failed to initialize AccountProtection: ", protection.GetLastErrorMessage());
        delete protection;
        return;
    }
    
    // Configure custom settings
    protection.SetMaxLossPercent(15.0);        // 15% max loss
    protection.SetMaxDrawdownPercent(25.0);    // 25% max drawdown
    protection.SetMaxOpenOrders(10);           // Max 10 open orders
    protection.SetMaxLotSize(1.0);             // Max 1.0 lot per trade
    
    // Check if trading is allowed
    if (protection.IsTradingAllowed())
    {
        Print("Trading is allowed");
        
        // Check if we can open a new order
        double lotSize = 0.5;
        if (protection.IsNewOrderAllowed(lotSize, Symbol()))
        {
            Print("New order with ", lotSize, " lots is allowed");
        }
        else
        {
            Print("New order is not allowed - risk limits exceeded");
        }
    }
    else
    {
        Print("Trading is not allowed - protection limits exceeded");
    }
    
    // Get current status
    Print("Current Status: ", protection.GetStatusDescription());
    Print("Current Loss: ", DoubleToString(protection.GetCurrentLossPercent(), 2), "%");
    Print("Current Drawdown: ", DoubleToString(protection.GetCurrentDrawdownPercent(), 2), "%");
    
    // Get comprehensive summary
    Print("\n", protection.GetProtectionSummary());
    
    // Cleanup
    delete protection;
}

//+------------------------------------------------------------------+
//| Example: Advanced Component Access                               |
//+------------------------------------------------------------------+
void ExampleAdvancedUsage()
{
    Print("=== Advanced Component Access Example ===");
    
    AccountProtection* protection = new AccountProtection(PROTECTION_STRICT);
    
    if (!protection.Initialize())
    {
        Print("Initialization failed");
        delete protection;
        return;
    }
    
    // Access individual components for advanced operations
    AccountProtectionConfig* config = protection.GetConfig();
    AccountMonitor* monitor = protection.GetMonitor();
    RiskChecker* riskChecker = protection.GetRiskChecker();
    ProtectionStatusManager* statusManager = protection.GetStatusManager();
    TradingController* tradingController = protection.GetTradingController();
    
    // Configure status thresholds
    if (statusManager != NULL)
    {
        statusManager.SetWarningThreshold(40.0);   // Warning at 40% of limit
        statusManager.SetCriticalThreshold(60.0);  // Critical at 60% of limit
        statusManager.SetEmergencyThreshold(80.0); // Emergency at 80% of limit
    }
    
    // Perform detailed risk checks
    if (riskChecker != NULL)
    {
        bool accountOK = riskChecker.CheckAccountLimits();
        bool positionOK = riskChecker.CheckPositionLimits(0.3);
        bool dailyOK = riskChecker.CheckDailyLimits();
        bool spreadOK = riskChecker.CheckSpreadLimits();
        
        Print("Risk Check Results:");
        Print("  Account Limits: ", (accountOK ? "OK" : "FAILED"));
        Print("  Position Limits: ", (positionOK ? "OK" : "FAILED"));
        Print("  Daily Limits: ", (dailyOK ? "OK" : "FAILED"));
        Print("  Spread Limits: ", (spreadOK ? "OK" : "FAILED"));
    }
    
    // Manual trading control
    if (tradingController != NULL)
    {
        // Temporarily disable new orders but allow modifications
        tradingController.SetAllowNewOrders(false);
        tradingController.SetAllowOrderModification(true);
        
        Print("Trading Control Status:");
        Print("  New Orders: ", (tradingController.IsNewOrdersAllowed() ? "ALLOWED" : "BLOCKED"));
        Print("  Modifications: ", (tradingController.IsOrderModificationAllowed() ? "ALLOWED" : "BLOCKED"));
        Print("  Closures: ", (tradingController.IsOrderClosureAllowed() ? "ALLOWED" : "BLOCKED"));
    }
    
    // Monitor detailed metrics
    if (monitor != NULL)
    {
        Print("Monitoring Details:");
        Print("  Initial Balance: ", DoubleToString(monitor.GetInitialBalance(), 2));
        Print("  Daily Start Balance: ", DoubleToString(monitor.GetDailyStartBalance(), 2));
        Print("  Max Equity: ", DoubleToString(monitor.GetMaxEquity(), 2));
        Print("  Daily Loss: ", DoubleToString(monitor.GetDailyLoss(), 2));
        Print("  Daily Profit: ", DoubleToString(monitor.GetDailyProfit(), 2));
    }
    
    delete protection;
}

//+------------------------------------------------------------------+
//| Example: Emergency Scenarios                                     |
//+------------------------------------------------------------------+
void ExampleEmergencyScenarios()
{
    Print("=== Emergency Scenarios Example ===");
    
    AccountProtection* protection = new AccountProtection(PROTECTION_MODERATE);
    
    if (!protection.Initialize())
    {
        delete protection;
        return;
    }
    
    // Simulate emergency stop
    protection.EmergencyStop("Market volatility too high");
    
    Print("After Emergency Stop:");
    Print("  Trading Halted: ", (protection.IsTradingHalted() ? "YES" : "NO"));
    Print("  Status: ", protection.GetStatusDescription());
    
    // Try to resume trading (should not work during emergency)
    protection.ResumeTrading();
    Print("After Resume Attempt:");
    Print("  Trading Halted: ", (protection.IsTradingHalted() ? "YES" : "NO"));
    
    // Clear emergency stop through trading controller
    TradingController* controller = protection.GetTradingController();
    if (controller != NULL)
    {
        controller.ClearEmergencyStop();
        Print("After Emergency Clear:");
        Print("  Trading Halted: ", (protection.IsTradingHalted() ? "YES" : "NO"));
        Print("  Status: ", protection.GetStatusDescription());
    }
    
    delete protection;
}

//+------------------------------------------------------------------+
//| Example: Integration with EA OnInit/OnTick/OnDeinit             |
//+------------------------------------------------------------------+
AccountProtection* g_protection = NULL;

bool InitializeProtection()
{
    g_protection = new AccountProtection(PROTECTION_MODERATE, true, Symbol());
    
    if (!g_protection.Initialize())
    {
        Print("Failed to initialize protection system: ", g_protection.GetLastErrorMessage());
        delete g_protection;
        g_protection = NULL;
        return false;
    }
    
    // Configure for this EA
    g_protection.SetMaxLossPercent(20.0);
    g_protection.SetMaxDrawdownPercent(30.0);
    g_protection.SetMaxOpenOrders(5);
    
    Print("Protection system initialized successfully");
    return true;
}

void UpdateProtection()
{
    if (g_protection != NULL)
    {
        g_protection.Update();
        
        // Log status changes
        ProtectionStatusManager* statusManager = g_protection.GetStatusManager();
        if (statusManager != NULL && statusManager.HasStatusChanged())
        {
            Print("Protection status changed to: ", g_protection.GetStatusDescription());
        }
    }
}

bool CanOpenNewTrade(double lotSize)
{
    if (g_protection == NULL)
        return false;
    
    return g_protection.IsNewOrderAllowed(lotSize, Symbol());
}

void CleanupProtection()
{
    if (g_protection != NULL)
    {
        delete g_protection;
        g_protection = NULL;
        Print("Protection system cleaned up");
    }
}

//+------------------------------------------------------------------+
//| Example: Usage in EA Main Functions                             |
//+------------------------------------------------------------------+
/*
// In your EA's OnInit()
int OnInit()
{
    if (!InitializeProtection())
    {
        return INIT_FAILED;
    }
    
    return INIT_SUCCEEDED;
}

// In your EA's OnTick()
void OnTick()
{
    UpdateProtection();
    
    // Check if we can trade before opening positions
    if (CanOpenNewTrade(0.1))
    {
        // Your trading logic here
        Print("Can open new trade");
    }
    else
    {
        Print("Cannot open new trade - protection limits");
    }
}

// In your EA's OnDeinit()
void OnDeinit(const int reason)
{
    CleanupProtection();
}
*/

#endif // EXAMPLE_USAGE_MQH
