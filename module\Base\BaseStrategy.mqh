//+------------------------------------------------------------------+
//|                                                 BaseStrategy.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef BASE_STRATEGY_MQH
#define BASE_STRATEGY_MQH

#include "BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Trade Direction Enumeration                                      |
//+------------------------------------------------------------------+
enum ENUM_TRADE_DIRECTION
{
    TRADE_NONE = 0,         // No trade
    TRADE_BUY = 1,          // Buy trade
    TRADE_SELL = -1,        // Sell trade
    TRADE_BOTH = 2          // Both directions allowed
};

//+------------------------------------------------------------------+
//| Strategy State Enumeration                                       |
//+------------------------------------------------------------------+
enum ENUM_STRATEGY_STATE
{
    STRATEGY_IDLE = 0,      // Strategy idle
    STRATEGY_ANALYZING = 1, // Analyzing market
    STRATEGY_SIGNALED = 2,  // Signal generated
    STRATEGY_TRADING = 3,   // Trade in progress
    STRATEGY_WAITING = 4    // Waiting for conditions
};

//+------------------------------------------------------------------+
//| Trade Signal Structure                                           |
//+------------------------------------------------------------------+
struct TradeSignal
{
    ENUM_TRADE_DIRECTION  direction;      // Trade direction
    double                entryPrice;     // Entry price
    double                stopLoss;       // Stop loss price
    double                takeProfit;     // Take profit price
    double                lotSize;        // Position size
    double                confidence;     // Signal confidence (0.0 - 1.0)
    datetime              timestamp;      // Signal timestamp
    string                comment;        // Signal comment
    int                   magicNumber;    // Magic number
};

//+------------------------------------------------------------------+
//| BaseStrategy Class                                               |
//| Base class for all trading strategy implementations             |
//+------------------------------------------------------------------+
class BaseStrategy : public BaseComponent
{
private:
    string                m_symbol;           // Trading symbol
    ENUM_TIMEFRAMES       m_timeframe;        // Strategy timeframe
    int                   m_magicNumber;      // Magic number
    ENUM_STRATEGY_STATE   m_state;            // Current strategy state
    TradeSignal           m_lastSignal;       // Last generated signal
    double                m_riskPercent;      // Risk percentage per trade
    double                m_maxSpread;        // Maximum allowed spread
    bool                  m_allowLong;        // Allow long positions
    bool                  m_allowShort;       // Allow short positions

protected:
    //--- Protected methods for derived classes
    void                  SetState(ENUM_STRATEGY_STATE state);
    void                  SetSignal(ENUM_TRADE_DIRECTION direction, double entryPrice, 
                                   double stopLoss, double takeProfit, double lotSize, 
                                   double confidence, string comment = "");
    bool                  IsSpreadAcceptable() const;
    bool                  IsDirectionAllowed(ENUM_TRADE_DIRECTION direction) const;
    double                CalculatePositionSize(double riskAmount, double stopLossDistance) const;

public:
    //--- Constructor and Destructor
                          BaseStrategy(string name, string symbol = "", 
                                     ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                     int magicNumber = 0);
    virtual              ~BaseStrategy();
    
    //--- Configuration methods
    void                  SetSymbol(string symbol) { m_symbol = symbol; }
    void                  SetTimeframe(ENUM_TIMEFRAMES timeframe) { m_timeframe = timeframe; }
    void                  SetMagicNumber(int magic) { m_magicNumber = magic; }
    void                  SetRiskPercent(double risk) { m_riskPercent = MathMax(0.1, MathMin(10.0, risk)); }
    void                  SetMaxSpread(double spread) { m_maxSpread = MathMax(0.0, spread); }
    void                  SetAllowLong(bool allow) { m_allowLong = allow; }
    void                  SetAllowShort(bool allow) { m_allowShort = allow; }
    
    //--- Information methods
    string                GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES       GetTimeframe() const { return m_timeframe; }
    int                   GetMagicNumber() const { return m_magicNumber; }
    ENUM_STRATEGY_STATE   GetState() const { return m_state; }
    double                GetRiskPercent() const { return m_riskPercent; }
    double                GetMaxSpread() const { return m_maxSpread; }
    bool                  IsLongAllowed() const { return m_allowLong; }
    bool                  IsShortAllowed() const { return m_allowShort; }
    
    //--- Signal methods
    TradeSignal           GetLastSignal() const { return m_lastSignal; }
    bool                  HasValidSignal() const;
    bool                  IsSignalExpired(int maxAgeSeconds = 60) const;
    
    //--- Strategy execution methods
    virtual bool          AnalyzeMarket() = 0;
    virtual TradeSignal   GenerateSignal() = 0;
    virtual bool          ValidateSignal(const TradeSignal& signal) const;
    virtual bool          ExecuteStrategy();
    
    //--- Market condition methods
    virtual bool          IsMarketOpen() const;
    virtual bool          IsTradingAllowed() const;
    virtual double        GetCurrentSpread() const;
    virtual double        GetAccountBalance() const;
    virtual double        GetAccountEquity() const;
    
    //--- Override base class methods
    virtual bool          OnInitialize() override;
    virtual bool          OnValidate() override;
    virtual bool          OnUpdate() override;
    virtual void          OnReset() override;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
BaseStrategy::BaseStrategy(string name, string symbol = "", 
                          ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                          int magicNumber = 0) : BaseComponent(name)
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_timeframe = (timeframe == PERIOD_CURRENT) ? Period() : timeframe;
    m_magicNumber = magicNumber;
    m_state = STRATEGY_IDLE;
    m_riskPercent = 2.0;
    m_maxSpread = 5.0;
    m_allowLong = true;
    m_allowShort = true;
    
    // Initialize signal structure
    m_lastSignal.direction = TRADE_NONE;
    m_lastSignal.entryPrice = 0.0;
    m_lastSignal.stopLoss = 0.0;
    m_lastSignal.takeProfit = 0.0;
    m_lastSignal.lotSize = 0.0;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.comment = "";
    m_lastSignal.magicNumber = m_magicNumber;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
BaseStrategy::~BaseStrategy()
{
    // Base destructor - cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize strategy                                              |
//+------------------------------------------------------------------+
bool BaseStrategy::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(201, "Invalid symbol");
        return false;
    }
    
    if (m_magicNumber <= 0)
    {
        SetError(202, "Invalid magic number");
        return false;
    }
    
    SetState(STRATEGY_IDLE);
    return true;
}

//+------------------------------------------------------------------+
//| Validate strategy parameters                                     |
//+------------------------------------------------------------------+
bool BaseStrategy::OnValidate()
{
    if (!IsMarketOpen())
    {
        SetError(203, "Market is closed");
        return false;
    }
    
    if (!IsTradingAllowed())
    {
        SetError(204, "Trading not allowed");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update strategy                                                  |
//+------------------------------------------------------------------+
bool BaseStrategy::OnUpdate()
{
    if (!IsTradingAllowed())
        return false;
    
    return ExecuteStrategy();
}

//+------------------------------------------------------------------+
//| Reset strategy                                                   |
//+------------------------------------------------------------------+
void BaseStrategy::OnReset()
{
    SetState(STRATEGY_IDLE);
    m_lastSignal.direction = TRADE_NONE;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
}

//+------------------------------------------------------------------+
//| Set strategy state                                               |
//+------------------------------------------------------------------+
void BaseStrategy::SetState(ENUM_STRATEGY_STATE state)
{
    m_state = state;
}

//+------------------------------------------------------------------+
//| Set trade signal                                                 |
//+------------------------------------------------------------------+
void BaseStrategy::SetSignal(ENUM_TRADE_DIRECTION direction, double entryPrice, 
                             double stopLoss, double takeProfit, double lotSize, 
                             double confidence, string comment = "")
{
    m_lastSignal.direction = direction;
    m_lastSignal.entryPrice = entryPrice;
    m_lastSignal.stopLoss = stopLoss;
    m_lastSignal.takeProfit = takeProfit;
    m_lastSignal.lotSize = lotSize;
    m_lastSignal.confidence = MathMax(0.0, MathMin(1.0, confidence));
    m_lastSignal.timestamp = TimeCurrent();
    m_lastSignal.comment = comment;
    m_lastSignal.magicNumber = m_magicNumber;
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable                                    |
//+------------------------------------------------------------------+
bool BaseStrategy::IsSpreadAcceptable() const
{
    return (GetCurrentSpread() <= m_maxSpread);
}

//+------------------------------------------------------------------+
//| Check if trade direction is allowed                              |
//+------------------------------------------------------------------+
bool BaseStrategy::IsDirectionAllowed(ENUM_TRADE_DIRECTION direction) const
{
    switch(direction)
    {
        case TRADE_BUY:  return m_allowLong;
        case TRADE_SELL: return m_allowShort;
        default:         return false;
    }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk                           |
//+------------------------------------------------------------------+
double BaseStrategy::CalculatePositionSize(double riskAmount, double stopLossDistance) const
{
    if (stopLossDistance <= 0.0 || riskAmount <= 0.0)
        return 0.0;
    
    double tickValue = MarketInfo(m_symbol, MODE_TICKVALUE);
    double tickSize = MarketInfo(m_symbol, MODE_TICKSIZE);
    
    if (tickValue <= 0.0 || tickSize <= 0.0)
        return 0.0;
    
    double riskInTicks = stopLossDistance / tickSize;
    double lotSize = riskAmount / (riskInTicks * tickValue);
    
    // Apply lot size constraints
    double minLot = MarketInfo(m_symbol, MODE_MINLOT);
    double maxLot = MarketInfo(m_symbol, MODE_MAXLOT);
    double lotStep = MarketInfo(m_symbol, MODE_LOTSTEP);
    
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    lotSize = MathRound(lotSize / lotStep) * lotStep;
    
    return lotSize;
}

//+------------------------------------------------------------------+
//| Check if signal is valid                                         |
//+------------------------------------------------------------------+
bool BaseStrategy::HasValidSignal() const
{
    return (m_lastSignal.direction != TRADE_NONE && 
            m_lastSignal.confidence > 0.0 && 
            !IsSignalExpired());
}

//+------------------------------------------------------------------+
//| Check if signal is expired                                       |
//+------------------------------------------------------------------+
bool BaseStrategy::IsSignalExpired(int maxAgeSeconds = 60) const
{
    return (TimeCurrent() - m_lastSignal.timestamp > maxAgeSeconds);
}

//+------------------------------------------------------------------+
//| Execute strategy                                                 |
//+------------------------------------------------------------------+
bool BaseStrategy::ExecuteStrategy()
{
    SetState(STRATEGY_ANALYZING);
    
    if (!AnalyzeMarket())
    {
        SetState(STRATEGY_IDLE);
        return false;
    }
    
    TradeSignal signal = GenerateSignal();
    if (signal.direction == TRADE_NONE)
    {
        SetState(STRATEGY_WAITING);
        return true;
    }
    
    if (!ValidateSignal(signal))
    {
        SetState(STRATEGY_IDLE);
        return false;
    }
    
    m_lastSignal = signal;
    SetState(STRATEGY_SIGNALED);
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate trade signal                                            |
//+------------------------------------------------------------------+
bool BaseStrategy::ValidateSignal(const TradeSignal& signal) const
{
    if (signal.direction == TRADE_NONE)
        return false;
    
    if (!IsDirectionAllowed(signal.direction))
        return false;
    
    if (!IsSpreadAcceptable())
        return false;
    
    if (signal.lotSize <= 0.0)
        return false;
    
    if (signal.confidence < 0.5)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if market is open                                          |
//+------------------------------------------------------------------+
bool BaseStrategy::IsMarketOpen() const
{
    return (MarketInfo(m_symbol, MODE_TRADEALLOWED) > 0);
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool BaseStrategy::IsTradingAllowed() const
{
    return (IsTradeAllowed() && IsMarketOpen());
}

//+------------------------------------------------------------------+
//| Get current spread                                               |
//+------------------------------------------------------------------+
double BaseStrategy::GetCurrentSpread() const
{
    return MarketInfo(m_symbol, MODE_SPREAD);
}

//+------------------------------------------------------------------+
//| Get account balance                                              |
//+------------------------------------------------------------------+
double BaseStrategy::GetAccountBalance() const
{
    return AccountBalance();
}

//+------------------------------------------------------------------+
//| Get account equity                                               |
//+------------------------------------------------------------------+
double BaseStrategy::GetAccountEquity() const
{
    return AccountEquity();
}

#endif // BASE_STRATEGY_MQH
