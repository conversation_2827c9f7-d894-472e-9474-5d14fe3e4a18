//+------------------------------------------------------------------+
//|                                      OrderManagerValidator.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ORDER_MANAGER_VALIDATOR_MQH
#define ORDER_MANAGER_VALIDATOR_MQH

#include "../../Utils/Validation/ParameterValidator.mqh"

//+------------------------------------------------------------------+
//| OrderManagerValidator Class                                     |
//| Specialized validator for order management parameters           |
//| Inherits from ParameterValidator base class                     |
//+------------------------------------------------------------------+
class OrderManagerValidator : public ParameterValidator
{
private:
    // Validation constants for order parameters
    static const double MIN_LOT_SIZE;
    static const double MAX_LOT_SIZE;
    static const double MIN_PRICE;
    static const double MAX_PRICE;
    static const int    MIN_SLIPPAGE;
    static const int    MAX_SLIPPAGE;
    static const int    MIN_MAGIC_NUMBER;
    static const int    MAX_MAGIC_NUMBER;
    static const int    MIN_RETRY_COUNT;
    static const int    MAX_RETRY_COUNT;
    static const int    MIN_RETRY_DELAY;
    static const int    MAX_RETRY_DELAY;
    static const double MIN_SPREAD;
    static const double MAX_SPREAD;
    static const double MIN_STOP_LEVEL;
    static const double MAX_STOP_LEVEL;

public:
    //--- Constructor and Destructor
                      OrderManagerValidator(bool strictMode = true);
    virtual          ~OrderManagerValidator();
    
    //--- Core parameter validation methods
    ValidationResult  ValidateLotSizeParameter(double lotSize, string symbol = "");
    ValidationResult  ValidatePriceLevels(double price, string symbol = "");
    ValidationResult  ValidateStopLossTakeProfit(double stopLoss, double takeProfit, 
                                                 double entryPrice, int orderType);
    
    //--- Trading condition validation methods
    ValidationResult  ValidateMarketConditions(string symbol = "");
    ValidationResult  ValidateSpreadCondition(double spread, string symbol = "");
    ValidationResult  ValidateTradingHours(string symbol = "");
    ValidationResult  ValidateSymbolValidity(string symbol);
    
    //--- Advanced order validation methods
    ValidationResult  ValidateSlippageParameter(int slippage);
    ValidationResult  ValidateMagicNumberParameter(int magicNumber);
    ValidationResult  ValidateRetrySettings(int maxRetries, int retryDelay);
    
    //--- Risk parameter validation methods
    ValidationResult  ValidatePositionLimits(int currentOrders, int maxOrders);
    ValidationResult  ValidateExposureControls(double lotSize, double totalExposure, double maxExposure);
    
    //--- Technical validation methods
    ValidationResult  ValidatePriceNormalization(double price, string symbol = "");
    ValidationResult  ValidateLotStepCompliance(double lotSize, string symbol = "");
    ValidationResult  ValidateStopLevelCompliance(double stopLoss, double takeProfit, 
                                                  double entryPrice, int orderType, string symbol = "");
    
    //--- Comprehensive validation methods
    bool              ValidateAllOrderParameters(
                          double lotSize,
                          double price,
                          double stopLoss,
                          double takeProfit,
                          int orderType,
                          int slippage,
                          int magicNumber,
                          string symbol = ""
                      );
    
    bool              ValidateOrderExecutionConditions(
                          string symbol = "",
                          double spread = 0.0,
                          int currentOrders = 0,
                          int maxOrders = 20
                      );
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Utility methods
    string            GetOrderTypeName(int orderType);
    bool              IsValidOrderType(int orderType);
    double            GetSymbolMinLot(string symbol = "");
    double            GetSymbolMaxLot(string symbol = "");
    double            GetSymbolLotStep(string symbol = "");
    int               GetSymbolStopLevel(string symbol = "");
    bool              IsSymbolTradingAllowed(string symbol = "");
    
private:
    //--- Internal validation helpers
    bool              ValidateOrderTypeSpecificLevels(double stopLoss, double takeProfit, 
                                                     double entryPrice, int orderType, string symbol);
    double            CalculateMinStopDistance(double entryPrice, int orderType, string symbol);
    bool              IsWithinTradingSession(string symbol);
    ValidationResult  CreateOrderValidationResult(bool isValid, string paramName, 
                                                  string errorMessage = "", string expectedRange = "");
};

//+------------------------------------------------------------------+
//| Static constants definition                                      |
//+------------------------------------------------------------------+
static const double OrderManagerValidator::MIN_LOT_SIZE = 0.01;
static const double OrderManagerValidator::MAX_LOT_SIZE = 100.0;
static const double OrderManagerValidator::MIN_PRICE = 0.00001;
static const double OrderManagerValidator::MAX_PRICE = 999999.0;
static const int    OrderManagerValidator::MIN_SLIPPAGE = 0;
static const int    OrderManagerValidator::MAX_SLIPPAGE = 100;
static const int    OrderManagerValidator::MIN_MAGIC_NUMBER = 1;
static const int    OrderManagerValidator::MAX_MAGIC_NUMBER = 2147483647;
static const int    OrderManagerValidator::MIN_RETRY_COUNT = 1;
static const int    OrderManagerValidator::MAX_RETRY_COUNT = 10;
static const int    OrderManagerValidator::MIN_RETRY_DELAY = 100;
static const int    OrderManagerValidator::MAX_RETRY_DELAY = 10000;
static const double OrderManagerValidator::MIN_SPREAD = 0.0;
static const double OrderManagerValidator::MAX_SPREAD = 50.0;
static const double OrderManagerValidator::MIN_STOP_LEVEL = 0.0;
static const double OrderManagerValidator::MAX_STOP_LEVEL = 1000.0;

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
OrderManagerValidator::OrderManagerValidator(bool strictMode = true) 
    : ParameterValidator(strictMode)
{
    // Set component name for logging
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
OrderManagerValidator::~OrderManagerValidator()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize validator                                             |
//+------------------------------------------------------------------+
bool OrderManagerValidator::OnInitialize()
{
    if (!ParameterValidator::OnInitialize())
    {
        return false;
    }

    // Additional initialization for order manager validator
    return true;
}

//+------------------------------------------------------------------+
//| Validate validator state                                         |
//+------------------------------------------------------------------+
bool OrderManagerValidator::OnValidate()
{
    return ParameterValidator::OnValidate();
}

//+------------------------------------------------------------------+
//| Validate lot size parameter                                      |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateLotSizeParameter(double lotSize, string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;

    // Get symbol-specific lot constraints
    double minLot = GetSymbolMinLot(currentSymbol);
    double maxLot = GetSymbolMaxLot(currentSymbol);
    double lotStep = GetSymbolLotStep(currentSymbol);

    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    // Check basic range
    if (lotSize < minLot || lotSize > maxLot)
    {
        isValid = false;
        errorMsg = "Lot size " + DoubleToString(lotSize, 2) + " is outside valid range for " + currentSymbol;
        expectedRange = DoubleToString(minLot, 2) + " - " + DoubleToString(maxLot, 2);
    }
    // Check lot step compliance
    else if (lotStep > 0.0)
    {
        double remainder = MathMod(lotSize, lotStep);
        if (remainder > 0.000001) // Small tolerance for floating point precision
        {
            isValid = false;
            errorMsg = "Lot size " + DoubleToString(lotSize, 2) + " does not comply with lot step " + DoubleToString(lotStep, 2);
            expectedRange = "Multiple of " + DoubleToString(lotStep, 2);
        }
    }

    ValidationResult result = CreateResult(isValid, "LotSize", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate price levels                                            |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidatePriceLevels(double price, string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;

    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    // Check basic price validity
    if (price <= 0.0 || price == EMPTY_VALUE)
    {
        isValid = false;
        errorMsg = "Invalid price value: " + DoubleToString(price, Digits);
        expectedRange = "Positive value > 0";
    }
    // Check reasonable price range
    else if (price < MIN_PRICE || price > MAX_PRICE)
    {
        isValid = false;
        errorMsg = "Price " + DoubleToString(price, Digits) + " is outside reasonable range";
        expectedRange = DoubleToString(MIN_PRICE, 5) + " - " + DoubleToString(MAX_PRICE, 2);
    }

    ValidationResult result = CreateResult(isValid, "Price", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate stop loss and take profit levels                       |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateStopLossTakeProfit(double stopLoss, double takeProfit,
                                                                   double entryPrice, int orderType)
{
    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    // Validate order type first
    if (!IsValidOrderType(orderType))
    {
        isValid = false;
        errorMsg = "Invalid order type: " + IntegerToString(orderType);
        expectedRange = "OP_BUY, OP_SELL, OP_BUYLIMIT, OP_SELLLIMIT, OP_BUYSTOP, OP_SELLSTOP";
    }
    else
    {
        // Validate stop loss and take profit logic based on order type
        if (!ValidateOrderTypeSpecificLevels(stopLoss, takeProfit, entryPrice, orderType, Symbol()))
        {
            isValid = false;
            errorMsg = "Stop loss or take profit levels are invalid for " + GetOrderTypeName(orderType);
            expectedRange = "Levels must respect order type direction and minimum stop distance";
        }
    }

    ValidationResult result = CreateResult(isValid, "StopLossTakeProfit", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate market conditions                                       |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateMarketConditions(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;

    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    // Check if trading is allowed for the symbol
    if (!IsSymbolTradingAllowed(currentSymbol))
    {
        isValid = false;
        errorMsg = "Trading is not allowed for symbol " + currentSymbol;
        expectedRange = "Symbol must allow trading";
    }
    // Check if within trading session
    else if (!IsWithinTradingSession(currentSymbol))
    {
        isValid = false;
        errorMsg = "Outside trading session for symbol " + currentSymbol;
        expectedRange = "Must be within trading hours";
    }

    ValidationResult result = CreateResult(isValid, "MarketConditions", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate spread condition                                        |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateSpreadCondition(double spread, string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;

    // Get current spread if not provided
    double currentSpread = spread;
    if (spread <= 0.0)
    {
        currentSpread = MarketInfo(currentSymbol, MODE_SPREAD);
    }

    bool isValid = (currentSpread >= MIN_SPREAD && currentSpread <= MAX_SPREAD);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Spread " + DoubleToString(currentSpread, 1) + " points is outside acceptable range";
        expectedRange = DoubleToString(MIN_SPREAD, 1) + " - " + DoubleToString(MAX_SPREAD, 1) + " points";
    }

    ValidationResult result = CreateResult(isValid, "Spread", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate trading hours                                           |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateTradingHours(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;

    bool isValid = IsWithinTradingSession(currentSymbol);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Current time is outside trading session for " + currentSymbol;
        expectedRange = "Must be within market trading hours";
    }

    ValidationResult result = CreateResult(isValid, "TradingHours", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate symbol validity                                         |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateSymbolValidity(string symbol)
{
    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    if (symbol == "")
    {
        isValid = false;
        errorMsg = "Symbol cannot be empty";
        expectedRange = "Valid symbol name required";
    }
    else if (MarketInfo(symbol, MODE_DIGITS) == 0)
    {
        isValid = false;
        errorMsg = "Symbol " + symbol + " is not available or invalid";
        expectedRange = "Valid tradeable symbol";
    }

    ValidationResult result = CreateResult(isValid, "Symbol", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate slippage parameter                                      |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateSlippageParameter(int slippage)
{
    bool isValid = (slippage >= MIN_SLIPPAGE && slippage <= MAX_SLIPPAGE);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Slippage " + IntegerToString(slippage) + " is outside valid range";
        expectedRange = IntegerToString(MIN_SLIPPAGE) + " - " + IntegerToString(MAX_SLIPPAGE) + " points";
    }

    ValidationResult result = CreateResult(isValid, "Slippage", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate magic number parameter                                  |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateMagicNumberParameter(int magicNumber)
{
    bool isValid = (magicNumber >= MIN_MAGIC_NUMBER && magicNumber <= MAX_MAGIC_NUMBER);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Magic number " + IntegerToString(magicNumber) + " is outside valid range";
        expectedRange = IntegerToString(MIN_MAGIC_NUMBER) + " - " + IntegerToString(MAX_MAGIC_NUMBER);
    }

    ValidationResult result = CreateResult(isValid, "MagicNumber", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate retry settings                                          |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateRetrySettings(int maxRetries, int retryDelay)
{
    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    // Validate retry count
    if (maxRetries < MIN_RETRY_COUNT || maxRetries > MAX_RETRY_COUNT)
    {
        isValid = false;
        errorMsg = "Max retries " + IntegerToString(maxRetries) + " is outside valid range";
        expectedRange = IntegerToString(MIN_RETRY_COUNT) + " - " + IntegerToString(MAX_RETRY_COUNT);
    }
    // Validate retry delay
    else if (retryDelay < MIN_RETRY_DELAY || retryDelay > MAX_RETRY_DELAY)
    {
        isValid = false;
        errorMsg = "Retry delay " + IntegerToString(retryDelay) + "ms is outside valid range";
        expectedRange = IntegerToString(MIN_RETRY_DELAY) + " - " + IntegerToString(MAX_RETRY_DELAY) + "ms";
    }

    ValidationResult result = CreateResult(isValid, "RetrySettings", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate position limits                                         |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidatePositionLimits(int currentOrders, int maxOrders)
{
    bool isValid = (currentOrders >= 0 && currentOrders <= maxOrders);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Current orders " + IntegerToString(currentOrders) + " exceeds maximum limit";
        expectedRange = "0 - " + IntegerToString(maxOrders);
    }

    ValidationResult result = CreateResult(isValid, "PositionLimits", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate exposure controls                                       |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateExposureControls(double lotSize, double totalExposure, double maxExposure)
{
    double newTotalExposure = totalExposure + lotSize;
    bool isValid = (newTotalExposure <= maxExposure);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "New total exposure " + DoubleToString(newTotalExposure, 2) + " would exceed maximum limit";
        expectedRange = "Total exposure <= " + DoubleToString(maxExposure, 2);
    }

    ValidationResult result = CreateResult(isValid, "ExposureControls", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate price normalization                                     |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidatePriceNormalization(double price, string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    int digits = (int)MarketInfo(currentSymbol, MODE_DIGITS);

    double normalizedPrice = NormalizeDouble(price, digits);
    bool isValid = (MathAbs(price - normalizedPrice) < 0.000001);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Price " + DoubleToString(price, digits + 2) + " is not properly normalized";
        expectedRange = "Price normalized to " + IntegerToString(digits) + " digits";
    }

    ValidationResult result = CreateResult(isValid, "PriceNormalization", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate lot step compliance                                     |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateLotStepCompliance(double lotSize, string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    double lotStep = GetSymbolLotStep(currentSymbol);

    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    if (lotStep > 0.0)
    {
        double remainder = MathMod(lotSize, lotStep);
        if (remainder > 0.000001)
        {
            isValid = false;
            errorMsg = "Lot size " + DoubleToString(lotSize, 2) + " does not comply with lot step";
            expectedRange = "Multiple of " + DoubleToString(lotStep, 2);
        }
    }

    ValidationResult result = CreateResult(isValid, "LotStepCompliance", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate stop level compliance                                   |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::ValidateStopLevelCompliance(double stopLoss, double takeProfit,
                                                                    double entryPrice, int orderType, string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    int stopLevel = GetSymbolStopLevel(currentSymbol);

    bool isValid = true;
    string errorMsg = "";
    string expectedRange = "";

    if (stopLevel > 0)
    {
        double minDistance = stopLevel * MarketInfo(currentSymbol, MODE_POINT);

        // Check stop loss distance
        if (stopLoss > 0.0)
        {
            double slDistance = MathAbs(entryPrice - stopLoss);
            if (slDistance < minDistance)
            {
                isValid = false;
                errorMsg = "Stop loss too close to entry price";
                expectedRange = "Minimum distance: " + IntegerToString(stopLevel) + " points";
            }
        }

        // Check take profit distance
        if (takeProfit > 0.0 && isValid)
        {
            double tpDistance = MathAbs(entryPrice - takeProfit);
            if (tpDistance < minDistance)
            {
                isValid = false;
                errorMsg = "Take profit too close to entry price";
                expectedRange = "Minimum distance: " + IntegerToString(stopLevel) + " points";
            }
        }
    }

    ValidationResult result = CreateResult(isValid, "StopLevelCompliance", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate all order parameters                                    |
//+------------------------------------------------------------------+
bool OrderManagerValidator::ValidateAllOrderParameters(
    double lotSize,
    double price,
    double stopLoss,
    double takeProfit,
    int orderType,
    int slippage,
    int magicNumber,
    string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    bool allValid = true;

    // Validate each parameter
    ValidationResult result1 = ValidateLotSizeParameter(lotSize, currentSymbol);
    ValidationResult result2 = ValidatePriceLevels(price, currentSymbol);
    ValidationResult result3 = ValidateStopLossTakeProfit(stopLoss, takeProfit, price, orderType);
    ValidationResult result4 = ValidateSlippageParameter(slippage);
    ValidationResult result5 = ValidateMagicNumberParameter(magicNumber);
    ValidationResult result6 = ValidateSymbolValidity(currentSymbol);
    ValidationResult result7 = ValidatePriceNormalization(price, currentSymbol);
    ValidationResult result8 = ValidateLotStepCompliance(lotSize, currentSymbol);
    ValidationResult result9 = ValidateStopLevelCompliance(stopLoss, takeProfit, price, orderType, currentSymbol);

    allValid = result1.isValid && result2.isValid && result3.isValid &&
               result4.isValid && result5.isValid && result6.isValid &&
               result7.isValid && result8.isValid && result9.isValid;

    return allValid;
}

//+------------------------------------------------------------------+
//| Validate order execution conditions                              |
//+------------------------------------------------------------------+
bool OrderManagerValidator::ValidateOrderExecutionConditions(
    string symbol = "",
    double spread = 0.0,
    int currentOrders = 0,
    int maxOrders = 20)
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    bool allValid = true;

    // Validate execution conditions
    ValidationResult result1 = ValidateMarketConditions(currentSymbol);
    ValidationResult result2 = ValidateSpreadCondition(spread, currentSymbol);
    ValidationResult result3 = ValidateTradingHours(currentSymbol);
    ValidationResult result4 = ValidatePositionLimits(currentOrders, maxOrders);

    allValid = result1.isValid && result2.isValid && result3.isValid && result4.isValid;

    return allValid;
}

//+------------------------------------------------------------------+
//| Get order type name                                              |
//+------------------------------------------------------------------+
string OrderManagerValidator::GetOrderTypeName(int orderType)
{
    switch(orderType)
    {
        case OP_BUY:        return "BUY";
        case OP_SELL:       return "SELL";
        case OP_BUYLIMIT:   return "BUY LIMIT";
        case OP_SELLLIMIT:  return "SELL LIMIT";
        case OP_BUYSTOP:    return "BUY STOP";
        case OP_SELLSTOP:   return "SELL STOP";
        default:            return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Check if order type is valid                                     |
//+------------------------------------------------------------------+
bool OrderManagerValidator::IsValidOrderType(int orderType)
{
    return (orderType >= OP_BUY && orderType <= OP_SELLSTOP);
}

//+------------------------------------------------------------------+
//| Get symbol minimum lot size                                      |
//+------------------------------------------------------------------+
double OrderManagerValidator::GetSymbolMinLot(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    return MarketInfo(currentSymbol, MODE_MINLOT);
}

//+------------------------------------------------------------------+
//| Get symbol maximum lot size                                      |
//+------------------------------------------------------------------+
double OrderManagerValidator::GetSymbolMaxLot(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    return MarketInfo(currentSymbol, MODE_MAXLOT);
}

//+------------------------------------------------------------------+
//| Get symbol lot step                                              |
//+------------------------------------------------------------------+
double OrderManagerValidator::GetSymbolLotStep(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    return MarketInfo(currentSymbol, MODE_LOTSTEP);
}

//+------------------------------------------------------------------+
//| Get symbol stop level                                            |
//+------------------------------------------------------------------+
int OrderManagerValidator::GetSymbolStopLevel(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    return (int)MarketInfo(currentSymbol, MODE_STOPLEVEL);
}

//+------------------------------------------------------------------+
//| Check if symbol trading is allowed                               |
//+------------------------------------------------------------------+
bool OrderManagerValidator::IsSymbolTradingAllowed(string symbol = "")
{
    string currentSymbol = (symbol == "") ? Symbol() : symbol;
    return (MarketInfo(currentSymbol, MODE_TRADEALLOWED) > 0);
}

//+------------------------------------------------------------------+
//| Private helper methods                                           |
//+------------------------------------------------------------------+

//+------------------------------------------------------------------+
//| Validate order type specific levels                              |
//+------------------------------------------------------------------+
bool OrderManagerValidator::ValidateOrderTypeSpecificLevels(double stopLoss, double takeProfit,
                                                           double entryPrice, int orderType, string symbol)
{
    if (stopLoss <= 0.0 && takeProfit <= 0.0)
    {
        return true; // No levels to validate
    }

    bool isValid = true;

    switch(orderType)
    {
        case OP_BUY:
            // For buy orders: SL < entry < TP
            if (stopLoss > 0.0 && stopLoss >= entryPrice) isValid = false;
            if (takeProfit > 0.0 && takeProfit <= entryPrice) isValid = false;
            break;

        case OP_SELL:
            // For sell orders: TP < entry < SL
            if (stopLoss > 0.0 && stopLoss <= entryPrice) isValid = false;
            if (takeProfit > 0.0 && takeProfit >= entryPrice) isValid = false;
            break;

        case OP_BUYLIMIT:
        case OP_BUYSTOP:
            // Same logic as OP_BUY for pending buy orders
            if (stopLoss > 0.0 && stopLoss >= entryPrice) isValid = false;
            if (takeProfit > 0.0 && takeProfit <= entryPrice) isValid = false;
            break;

        case OP_SELLLIMIT:
        case OP_SELLSTOP:
            // Same logic as OP_SELL for pending sell orders
            if (stopLoss > 0.0 && stopLoss <= entryPrice) isValid = false;
            if (takeProfit > 0.0 && takeProfit >= entryPrice) isValid = false;
            break;

        default:
            isValid = false;
            break;
    }

    return isValid;
}

//+------------------------------------------------------------------+
//| Calculate minimum stop distance                                  |
//+------------------------------------------------------------------+
double OrderManagerValidator::CalculateMinStopDistance(double entryPrice, int orderType, string symbol)
{
    int stopLevel = GetSymbolStopLevel(symbol);
    double point = MarketInfo(symbol, MODE_POINT);

    return stopLevel * point;
}

//+------------------------------------------------------------------+
//| Check if within trading session                                  |
//+------------------------------------------------------------------+
bool OrderManagerValidator::IsWithinTradingSession(string symbol)
{
    // Basic implementation - can be enhanced with specific trading session logic
    datetime currentTime = TimeCurrent();
    int hour = TimeHour(currentTime);
    int dayOfWeek = TimeDayOfWeek(currentTime);

    // Avoid weekends
    if (dayOfWeek == 0 || dayOfWeek == 6)
    {
        return false;
    }

    // Basic trading hours check (can be customized per symbol)
    // Assuming 24/5 forex market
    return true;
}

//+------------------------------------------------------------------+
//| Create order validation result                                   |
//+------------------------------------------------------------------+
ValidationResult OrderManagerValidator::CreateOrderValidationResult(bool isValid, string paramName,
                                                                    string errorMessage = "", string expectedRange = "")
{
    return CreateResult(isValid, paramName, errorMessage, expectedRange);
}

#endif // ORDER_MANAGER_VALIDATOR_MQH
