//+------------------------------------------------------------------+
//|                                           PositionTracker.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef POSITION_TRACKER_MQH
#define POSITION_TRACKER_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Position Information Structure                                   |
//+------------------------------------------------------------------+
struct PositionInfo
{
    int               ticket;           // Order ticket
    int               type;             // Order type (OP_BUY/OP_SELL)
    double            lotSize;          // Position size
    double            openPrice;        // Open price
    datetime          openTime;         // Open time
    double            stopLoss;         // Stop loss level
    double            takeProfit;       // Take profit level
    double            currentPrice;     // Current market price
    double            profit;           // Current profit/loss
    double            swap;             // Swap charges
    double            commission;       // Commission
    double            totalPL;          // Total P&L (profit + swap + commission)
    string            comment;          // Order comment
    int               magicNumber;      // Magic number
};

//+------------------------------------------------------------------+
//| Position Statistics Structure                                    |
//+------------------------------------------------------------------+
struct PositionStats
{
    int               totalPositions;   // Total open positions
    int               buyPositions;     // Number of buy positions
    int               sellPositions;    // Number of sell positions
    double            totalLots;        // Total lot size
    double            buyLots;          // Buy positions lot size
    double            sellLots;         // Sell positions lot size
    double            totalProfit;      // Total profit/loss
    double            buyProfit;        // Buy positions profit
    double            sellProfit;       // Sell positions profit
    double            unrealizedPL;     // Unrealized P&L
    double            realizedPL;       // Realized P&L (from history)
};

//+------------------------------------------------------------------+
//| PositionTracker Class                                            |
//| Implementation of position monitoring and tracking system       |
//+------------------------------------------------------------------+
class PositionTracker : public BaseComponent
{
private:
    string            m_symbol;             // Trading symbol
    int               m_magicNumber;        // Magic number filter
    bool              m_trackAllSymbols;    // Track all symbols or just specified
    bool              m_trackAllMagics;     // Track all magic numbers or just specified
    
    // Position tracking
    PositionInfo      m_positions[];        // Array of position information
    PositionStats     m_currentStats;       // Current position statistics
    PositionStats     m_dailyStats;         // Daily statistics
    
    // Monitoring settings
    bool              m_enableAlerts;       // Enable profit/loss alerts
    double            m_profitAlert;        // Profit alert threshold
    double            m_lossAlert;          // Loss alert threshold
    datetime          m_lastUpdate;         // Last update time
    int               m_updateInterval;     // Update interval in seconds

public:
    //--- Constructor and Destructor
                      PositionTracker(string symbol = "", int magicNumber = 0);
    virtual          ~PositionTracker();
    
    //--- Configuration methods
    void              SetSymbol(string symbol) { m_symbol = symbol; }
    void              SetMagicNumber(int magic) { m_magicNumber = magic; }
    void              SetTrackAllSymbols(bool track) { m_trackAllSymbols = track; }
    void              SetTrackAllMagics(bool track) { m_trackAllMagics = track; }
    void              SetAlertThresholds(double profitAlert, double lossAlert);
    void              SetUpdateInterval(int seconds) { m_updateInterval = MathMax(1, seconds); }
    
    //--- Information methods
    string            GetSymbol() const { return m_symbol; }
    int               GetMagicNumber() const { return m_magicNumber; }
    PositionStats     GetCurrentStats() const { return m_currentStats; }
    PositionStats     GetDailyStats() const { return m_dailyStats; }
    int               GetPositionCount() const { return ArraySize(m_positions); }
    
    //--- Position tracking methods
    void              UpdatePositions();
    void              UpdateStatistics();
    PositionInfo      GetPosition(int index);
    PositionInfo      GetPositionByTicket(int ticket);
    bool              IsPositionOpen(int ticket);
    
    //--- Position analysis methods
    double            GetAverageOpenPrice(int orderType = -1);
    double            GetLargestPosition();
    double            GetSmallestPosition();
    datetime          GetOldestPositionTime();
    datetime          GetNewestPositionTime();
    double            GetPositionExposure();
    
    //--- Risk monitoring methods
    double            GetDrawdown();
    double            GetMaxDrawdown();
    double            GetProfitFactor();
    double            GetWinRate();
    bool              CheckRiskLimits();
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;
    
    //--- Utility methods
    void              PrintPositionSummary();
    void              PrintDetailedPositions();
    string            PositionTypeToString(int type);
    void              CheckAlerts();
    void              ResetDailyStats();
    
private:
    //--- Internal methods
    bool              ShouldTrackPosition(const PositionInfo& position);
    void              CalculatePositionPL(PositionInfo& position);
    void              AddPositionToStats(const PositionInfo& position, PositionStats& stats);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
PositionTracker::PositionTracker(string symbol = "", int magicNumber = 0) : BaseComponent("PositionTracker")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_magicNumber = magicNumber;
    m_trackAllSymbols = (symbol == "");
    m_trackAllMagics = (magicNumber == 0);
    
    m_enableAlerts = false;
    m_profitAlert = 1000.0;
    m_lossAlert = -500.0;
    m_lastUpdate = 0;
    m_updateInterval = 5; // 5 seconds
    
    // Initialize statistics
    ZeroMemory(m_currentStats);
    ZeroMemory(m_dailyStats);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
PositionTracker::~PositionTracker()
{
    ArrayFree(m_positions);
}

//+------------------------------------------------------------------+
//| Initialize position tracker                                      |
//+------------------------------------------------------------------+
bool PositionTracker::OnInitialize()
{
    UpdatePositions();
    UpdateStatistics();
    m_lastUpdate = TimeCurrent();
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool PositionTracker::OnValidate()
{
    if (!m_trackAllSymbols && m_symbol == "")
    {
        SetError(1301, "Invalid symbol for position tracking");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update position tracker                                          |
//+------------------------------------------------------------------+
bool PositionTracker::OnUpdate()
{
    datetime currentTime = TimeCurrent();
    
    if (currentTime - m_lastUpdate >= m_updateInterval)
    {
        UpdatePositions();
        UpdateStatistics();
        
        if (m_enableAlerts)
        {
            CheckAlerts();
        }
        
        m_lastUpdate = currentTime;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Set alert thresholds                                             |
//+------------------------------------------------------------------+
void PositionTracker::SetAlertThresholds(double profitAlert, double lossAlert)
{
    m_profitAlert = profitAlert;
    m_lossAlert = lossAlert;
    m_enableAlerts = true;
}

//+------------------------------------------------------------------+
//| Update positions array                                           |
//+------------------------------------------------------------------+
void PositionTracker::UpdatePositions()
{
    ArrayFree(m_positions);
    
    int totalOrders = OrdersTotal();
    int positionCount = 0;
    
    // Count relevant positions first
    for (int i = 0; i < totalOrders; i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            PositionInfo tempPos;
            tempPos.ticket = OrderTicket();
            tempPos.type = OrderType();
            tempPos.lotSize = OrderLots();
            tempPos.openPrice = OrderOpenPrice();
            tempPos.openTime = OrderOpenTime();
            tempPos.stopLoss = OrderStopLoss();
            tempPos.takeProfit = OrderTakeProfit();
            tempPos.profit = OrderProfit();
            tempPos.swap = OrderSwap();
            tempPos.commission = OrderCommission();
            tempPos.comment = OrderComment();
            tempPos.magicNumber = OrderMagicNumber();
            
            if (ShouldTrackPosition(tempPos))
            {
                positionCount++;
            }
        }
    }
    
    // Resize array and populate
    ArrayResize(m_positions, positionCount);
    int index = 0;
    
    for (int i = 0; i < totalOrders; i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            PositionInfo pos;
            pos.ticket = OrderTicket();
            pos.type = OrderType();
            pos.lotSize = OrderLots();
            pos.openPrice = OrderOpenPrice();
            pos.openTime = OrderOpenTime();
            pos.stopLoss = OrderStopLoss();
            pos.takeProfit = OrderTakeProfit();
            pos.profit = OrderProfit();
            pos.swap = OrderSwap();
            pos.commission = OrderCommission();
            pos.comment = OrderComment();
            pos.magicNumber = OrderMagicNumber();
            
            if (ShouldTrackPosition(pos))
            {
                // Calculate current price and P&L
                CalculatePositionPL(pos);
                
                m_positions[index] = pos;
                index++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Update statistics                                                |
//+------------------------------------------------------------------+
void PositionTracker::UpdateStatistics()
{
    // Reset current statistics
    ZeroMemory(m_currentStats);
    
    // Calculate statistics from positions
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        AddPositionToStats(m_positions[i], m_currentStats);
    }
}

//+------------------------------------------------------------------+
//| Get position by index                                            |
//+------------------------------------------------------------------+
PositionInfo PositionTracker::GetPosition(int index)
{
    PositionInfo emptyPos;
    ZeroMemory(emptyPos);
    
    if (index >= 0 && index < ArraySize(m_positions))
    {
        return m_positions[index];
    }
    
    return emptyPos;
}

//+------------------------------------------------------------------+
//| Get position by ticket                                           |
//+------------------------------------------------------------------+
PositionInfo PositionTracker::GetPositionByTicket(int ticket)
{
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        if (m_positions[i].ticket == ticket)
        {
            return m_positions[i];
        }
    }
    
    PositionInfo emptyPos;
    ZeroMemory(emptyPos);
    return emptyPos;
}

//+------------------------------------------------------------------+
//| Check if position is open                                        |
//+------------------------------------------------------------------+
bool PositionTracker::IsPositionOpen(int ticket)
{
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        if (m_positions[i].ticket == ticket)
        {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get average open price                                           |
//+------------------------------------------------------------------+
double PositionTracker::GetAverageOpenPrice(int orderType = -1)
{
    double totalValue = 0.0;
    double totalLots = 0.0;
    
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        if (orderType == -1 || m_positions[i].type == orderType)
        {
            totalValue += m_positions[i].openPrice * m_positions[i].lotSize;
            totalLots += m_positions[i].lotSize;
        }
    }
    
    return (totalLots > 0.0) ? totalValue / totalLots : 0.0;
}

//+------------------------------------------------------------------+
//| Get largest position size                                        |
//+------------------------------------------------------------------+
double PositionTracker::GetLargestPosition()
{
    double largest = 0.0;
    
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        if (m_positions[i].lotSize > largest)
        {
            largest = m_positions[i].lotSize;
        }
    }
    
    return largest;
}

//+------------------------------------------------------------------+
//| Get position exposure (net position)                            |
//+------------------------------------------------------------------+
double PositionTracker::GetPositionExposure()
{
    return m_currentStats.buyLots - m_currentStats.sellLots;
}

//+------------------------------------------------------------------+
//| Print position summary                                           |
//+------------------------------------------------------------------+
void PositionTracker::PrintPositionSummary()
{
    Print("=== Position Summary ===");
    Print("Total Positions: ", m_currentStats.totalPositions);
    Print("Buy Positions: ", m_currentStats.buyPositions, " (", DoubleToString(m_currentStats.buyLots, 2), " lots)");
    Print("Sell Positions: ", m_currentStats.sellPositions, " (", DoubleToString(m_currentStats.sellLots, 2), " lots)");
    Print("Total P&L: ", DoubleToString(m_currentStats.totalProfit, 2));
    Print("Net Exposure: ", DoubleToString(GetPositionExposure(), 2), " lots");
}

//+------------------------------------------------------------------+
//| Print detailed positions                                         |
//+------------------------------------------------------------------+
void PositionTracker::PrintDetailedPositions()
{
    Print("=== Detailed Positions ===");
    
    for (int i = 0; i < ArraySize(m_positions); i++)
    {
        PositionInfo pos = m_positions[i];
        Print("Ticket: ", pos.ticket, 
              " | Type: ", PositionTypeToString(pos.type),
              " | Lots: ", DoubleToString(pos.lotSize, 2),
              " | Open: ", DoubleToString(pos.openPrice, Digits),
              " | Current: ", DoubleToString(pos.currentPrice, Digits),
              " | P&L: ", DoubleToString(pos.totalPL, 2));
    }
}

//+------------------------------------------------------------------+
//| Convert position type to string                                 |
//+------------------------------------------------------------------+
string PositionTracker::PositionTypeToString(int type)
{
    switch(type)
    {
        case OP_BUY:  return "BUY";
        case OP_SELL: return "SELL";
        default:      return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Check alerts                                                     |
//+------------------------------------------------------------------+
void PositionTracker::CheckAlerts()
{
    if (m_currentStats.totalProfit >= m_profitAlert)
    {
        Alert("PROFIT ALERT: Total profit reached ", DoubleToString(m_currentStats.totalProfit, 2));
    }
    
    if (m_currentStats.totalProfit <= m_lossAlert)
    {
        Alert("LOSS ALERT: Total loss reached ", DoubleToString(m_currentStats.totalProfit, 2));
    }
}

//+------------------------------------------------------------------+
//| Check if position should be tracked                             |
//+------------------------------------------------------------------+
bool PositionTracker::ShouldTrackPosition(const PositionInfo& position)
{
    // Check symbol filter
    if (!m_trackAllSymbols && OrderSymbol() != m_symbol)
        return false;
    
    // Check magic number filter
    if (!m_trackAllMagics && position.magicNumber != m_magicNumber)
        return false;
    
    // Only track market orders (OP_BUY and OP_SELL)
    if (position.type != OP_BUY && position.type != OP_SELL)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate position P&L                                          |
//+------------------------------------------------------------------+
void PositionTracker::CalculatePositionPL(PositionInfo& position)
{
    string symbol = OrderSymbol();
    
    if (position.type == OP_BUY)
    {
        position.currentPrice = MarketInfo(symbol, MODE_BID);
    }
    else if (position.type == OP_SELL)
    {
        position.currentPrice = MarketInfo(symbol, MODE_ASK);
    }
    
    position.totalPL = position.profit + position.swap + position.commission;
}

//+------------------------------------------------------------------+
//| Add position to statistics                                       |
//+------------------------------------------------------------------+
void PositionTracker::AddPositionToStats(const PositionInfo& position, PositionStats& stats)
{
    stats.totalPositions++;
    stats.totalLots += position.lotSize;
    stats.totalProfit += position.totalPL;
    
    if (position.type == OP_BUY)
    {
        stats.buyPositions++;
        stats.buyLots += position.lotSize;
        stats.buyProfit += position.totalPL;
    }
    else if (position.type == OP_SELL)
    {
        stats.sellPositions++;
        stats.sellLots += position.lotSize;
        stats.sellProfit += position.totalPL;
    }
}

#endif // POSITION_TRACKER_MQH
