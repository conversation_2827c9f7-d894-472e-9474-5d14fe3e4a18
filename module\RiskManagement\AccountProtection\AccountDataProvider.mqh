//+------------------------------------------------------------------+
//|                                        AccountDataProvider.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_DATA_PROVIDER_MQH
#define ACCOUNT_DATA_PROVIDER_MQH

#include "../../Base/BaseComponent.mqh"

//+------------------------------------------------------------------+
//| AccountDataProvider Class                                        |
//| Responsible for providing account and market data               |
//| Single Responsibility: Data Retrieval                           |
//+------------------------------------------------------------------+
class AccountDataProvider : public BaseComponent
{
private:
    string                  m_symbol;               // Current symbol
    
public:
    //--- Constructor and Destructor
                            AccountDataProvider(string symbol = "");
    virtual                ~AccountDataProvider();
    
    //--- Configuration methods
    void                    SetSymbol(string symbol) { m_symbol = symbol; }
    string                  GetSymbol() const { return m_symbol; }
    
    //--- Account data methods
    double                  GetAccountBalance() const;
    double                  GetAccountEquity() const;
    double                  GetAccountProfit() const;
    double                  GetAccountMargin() const;
    double                  GetAccountFreeMargin() const;
    double                  GetAccountCredit() const;
    string                  GetAccountCurrency() const;
    int                     GetAccountLeverage() const;
    
    //--- Trading data methods
    int                     GetOpenOrdersCount() const;
    double                  GetTotalLotSize() const;
    double                  GetTotalProfit() const;
    double                  GetTotalLoss() const;
    int                     GetBuyOrdersCount() const;
    int                     GetSellOrdersCount() const;
    double                  GetBuyLotSize() const;
    double                  GetSellLotSize() const;
    
    //--- Market data methods
    double                  GetCurrentSpread(string symbol = "") const;
    double                  GetBid(string symbol = "") const;
    double                  GetAsk(string symbol = "") const;
    double                  GetPoint(string symbol = "") const;
    int                     GetDigits(string symbol = "") const;
    double                  GetTickValue(string symbol = "") const;
    double                  GetTickSize(string symbol = "") const;
    
    //--- Time data methods
    datetime                GetCurrentTime() const;
    datetime                GetServerTime() const;
    bool                    IsMarketOpen() const;
    bool                    IsTradingAllowed() const;
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    
    //--- Utility methods
    bool                    IsValidSymbol(string symbol) const;
    string                  GetDataSummary() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountDataProvider::AccountDataProvider(string symbol = "") : BaseComponent("AccountDataProvider")
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountDataProvider::~AccountDataProvider()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize data provider                                         |
//+------------------------------------------------------------------+
bool AccountDataProvider::OnInitialize()
{
    if (m_symbol == "")
    {
        m_symbol = Symbol();
    }
    
    if (!IsValidSymbol(m_symbol))
    {
        SetError(2001, "Invalid symbol: " + m_symbol);
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate data provider                                           |
//+------------------------------------------------------------------+
bool AccountDataProvider::OnValidate()
{
    if (!IsValidSymbol(m_symbol))
    {
        SetError(2002, "Symbol validation failed: " + m_symbol);
        return false;
    }
    
    if (GetAccountBalance() <= 0.0)
    {
        SetError(2003, "Invalid account balance");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Get account balance                                              |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAccountBalance() const
{
    return AccountBalance();
}

//+------------------------------------------------------------------+
//| Get account equity                                               |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAccountEquity() const
{
    return AccountEquity();
}

//+------------------------------------------------------------------+
//| Get account profit                                               |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAccountProfit() const
{
    return AccountProfit();
}

//+------------------------------------------------------------------+
//| Get account margin                                               |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAccountMargin() const
{
    return AccountMargin();
}

//+------------------------------------------------------------------+
//| Get account free margin                                          |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAccountFreeMargin() const
{
    return AccountFreeMargin();
}

//+------------------------------------------------------------------+
//| Get account credit                                               |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAccountCredit() const
{
    return AccountCredit();
}

//+------------------------------------------------------------------+
//| Get account currency                                             |
//+------------------------------------------------------------------+
string AccountDataProvider::GetAccountCurrency() const
{
    return AccountCurrency();
}

//+------------------------------------------------------------------+
//| Get account leverage                                             |
//+------------------------------------------------------------------+
int AccountDataProvider::GetAccountLeverage() const
{
    return AccountLeverage();
}

//+------------------------------------------------------------------+
//| Get open orders count                                            |
//+------------------------------------------------------------------+
int AccountDataProvider::GetOpenOrdersCount() const
{
    return OrdersTotal();
}

//+------------------------------------------------------------------+
//| Get total lot size                                               |
//+------------------------------------------------------------------+
double AccountDataProvider::GetTotalLotSize() const
{
    double totalLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            totalLots += OrderLots();
        }
    }
    
    return totalLots;
}

//+------------------------------------------------------------------+
//| Get total profit                                                 |
//+------------------------------------------------------------------+
double AccountDataProvider::GetTotalProfit() const
{
    double totalProfit = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderProfit() > 0.0)
                totalProfit += OrderProfit();
        }
    }
    
    return totalProfit;
}

//+------------------------------------------------------------------+
//| Get total loss                                                   |
//+------------------------------------------------------------------+
double AccountDataProvider::GetTotalLoss() const
{
    double totalLoss = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderProfit() < 0.0)
                totalLoss += MathAbs(OrderProfit());
        }
    }
    
    return totalLoss;
}

//+------------------------------------------------------------------+
//| Get buy orders count                                             |
//+------------------------------------------------------------------+
int AccountDataProvider::GetBuyOrdersCount() const
{
    int buyCount = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderType() == OP_BUY || OrderType() == OP_BUYLIMIT || OrderType() == OP_BUYSTOP)
                buyCount++;
        }
    }
    
    return buyCount;
}

//+------------------------------------------------------------------+
//| Get sell orders count                                            |
//+------------------------------------------------------------------+
int AccountDataProvider::GetSellOrdersCount() const
{
    int sellCount = 0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderType() == OP_SELL || OrderType() == OP_SELLLIMIT || OrderType() == OP_SELLSTOP)
                sellCount++;
        }
    }
    
    return sellCount;
}

//+------------------------------------------------------------------+
//| Get buy lot size                                                 |
//+------------------------------------------------------------------+
double AccountDataProvider::GetBuyLotSize() const
{
    double buyLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderType() == OP_BUY || OrderType() == OP_BUYLIMIT || OrderType() == OP_BUYSTOP)
                buyLots += OrderLots();
        }
    }
    
    return buyLots;
}

//+------------------------------------------------------------------+
//| Get sell lot size                                                |
//+------------------------------------------------------------------+
double AccountDataProvider::GetSellLotSize() const
{
    double sellLots = 0.0;
    
    for (int i = 0; i < OrdersTotal(); i++)
    {
        if (OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
        {
            if (OrderType() == OP_SELL || OrderType() == OP_SELLLIMIT || OrderType() == OP_SELLSTOP)
                sellLots += OrderLots();
        }
    }
    
    return sellLots;
}

//+------------------------------------------------------------------+
//| Get current spread                                               |
//+------------------------------------------------------------------+
double AccountDataProvider::GetCurrentSpread(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return MarketInfo(symbol, MODE_SPREAD);
}

//+------------------------------------------------------------------+
//| Get bid price                                                    |
//+------------------------------------------------------------------+
double AccountDataProvider::GetBid(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return MarketInfo(symbol, MODE_BID);
}

//+------------------------------------------------------------------+
//| Get ask price                                                    |
//+------------------------------------------------------------------+
double AccountDataProvider::GetAsk(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return MarketInfo(symbol, MODE_ASK);
}

//+------------------------------------------------------------------+
//| Get point value                                                  |
//+------------------------------------------------------------------+
double AccountDataProvider::GetPoint(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return MarketInfo(symbol, MODE_POINT);
}

//+------------------------------------------------------------------+
//| Get digits                                                       |
//+------------------------------------------------------------------+
int AccountDataProvider::GetDigits(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return (int)MarketInfo(symbol, MODE_DIGITS);
}

//+------------------------------------------------------------------+
//| Get tick value                                                   |
//+------------------------------------------------------------------+
double AccountDataProvider::GetTickValue(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return MarketInfo(symbol, MODE_TICKVALUE);
}

//+------------------------------------------------------------------+
//| Get tick size                                                    |
//+------------------------------------------------------------------+
double AccountDataProvider::GetTickSize(string symbol = "") const
{
    if (symbol == "")
        symbol = m_symbol;
    
    return MarketInfo(symbol, MODE_TICKSIZE);
}

//+------------------------------------------------------------------+
//| Get current time                                                 |
//+------------------------------------------------------------------+
datetime AccountDataProvider::GetCurrentTime() const
{
    return TimeCurrent();
}

//+------------------------------------------------------------------+
//| Get server time                                                  |
//+------------------------------------------------------------------+
datetime AccountDataProvider::GetServerTime() const
{
    return TimeCurrent();
}

//+------------------------------------------------------------------+
//| Check if market is open                                          |
//+------------------------------------------------------------------+
bool AccountDataProvider::IsMarketOpen() const
{
    return MarketInfo(m_symbol, MODE_TRADEALLOWED) > 0;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool AccountDataProvider::IsTradingAllowed() const
{
    return IsTradeAllowed() && IsMarketOpen();
}

//+------------------------------------------------------------------+
//| Check if symbol is valid                                         |
//+------------------------------------------------------------------+
bool AccountDataProvider::IsValidSymbol(string symbol) const
{
    return MarketInfo(symbol, MODE_BID) > 0;
}

//+------------------------------------------------------------------+
//| Get data summary                                                 |
//+------------------------------------------------------------------+
string AccountDataProvider::GetDataSummary() const
{
    string summary = StringFormat("Account: Balance=%.2f, Equity=%.2f, Profit=%.2f", 
                                 GetAccountBalance(), GetAccountEquity(), GetAccountProfit());
    summary += StringFormat("\nOrders: Total=%d, Buy=%d, Sell=%d", 
                           GetOpenOrdersCount(), GetBuyOrdersCount(), GetSellOrdersCount());
    summary += StringFormat("\nLots: Total=%.2f, Buy=%.2f, Sell=%.2f", 
                           GetTotalLotSize(), GetBuyLotSize(), GetSellLotSize());
    summary += StringFormat("\nMarket: Spread=%.1f, Bid=%.5f, Ask=%.5f", 
                           GetCurrentSpread(), GetBid(), GetAsk());
    
    return summary;
}

#endif // ACCOUNT_DATA_PROVIDER_MQH
