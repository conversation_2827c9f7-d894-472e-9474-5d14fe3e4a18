//+------------------------------------------------------------------+
//|                                               BollingerBands.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef BOLLINGER_BANDS_MQH
#define BOLLINGER_BANDS_MQH

#include "../../Base/BaseIndicator.mqh"

//+------------------------------------------------------------------+
//| BollingerBands Class                                             |
//| Implementation of Bollinger Bands technical indicator           |
//+------------------------------------------------------------------+
class BollingerBands : public BaseIndicator
{
private:
    int               m_period;           // Moving average period
    double            m_deviation;        // Standard deviation multiplier
    int               m_maMethod;         // Moving average method
    int               m_appliedPrice;     // Applied price type
    
    // Bollinger Bands values
    double            m_upperBand;        // Upper band value
    double            m_lowerBand;        // Lower band value
    double            m_middleBand;       // Middle band (MA) value
    double            m_bandwidth;        // Band width
    double            m_percentB;         // %B indicator

public:
    //--- Constructor and Destructor
                      BollingerBands(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                                    int period = 20, double deviation = 2.0,
                                    int maMethod = MODE_SMA, int appliedPrice = PRICE_CLOSE);
    virtual          ~BollingerBands();
    
    //--- Configuration methods
    void              SetPeriod(int period) { m_period = MathMax(1, period); }
    void              SetDeviation(double deviation) { m_deviation = MathMax(0.1, deviation); }
    void              SetMAMethod(int method) { m_maMethod = method; }
    void              SetAppliedPrice(int price) { m_appliedPrice = price; }
    
    //--- Information methods
    int               GetBBPeriod() const { return m_period; }
    double            GetDeviation() const { return m_deviation; }
    int               GetMAMethod() const { return m_maMethod; }
    int               GetAppliedPrice() const { return m_appliedPrice; }
    
    //--- Bollinger Bands values
    double            GetUpperBand(int shift = 0) const;
    double            GetLowerBand(int shift = 0) const;
    double            GetMiddleBand(int shift = 0) const;
    double            GetBandwidth(int shift = 0) const;
    double            GetPercentB(int shift = 0) const;
    
    //--- Override base class methods
    virtual double    Calculate(int shift = 0) override;
    virtual SignalInfo GenerateSignal(int shift = 0) override;
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Signal analysis methods
    bool              IsPriceAboveUpperBand(int shift = 0) const;
    bool              IsPriceBelowLowerBand(int shift = 0) const;
    bool              IsPriceNearUpperBand(int shift = 0, double threshold = 0.1) const;
    bool              IsPriceNearLowerBand(int shift = 0, double threshold = 0.1) const;
    bool              IsBandwidthExpanding(int shift = 0) const;
    bool              IsBandwidthContracting(int shift = 0) const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
BollingerBands::BollingerBands(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                              int period = 20, double deviation = 2.0,
                              int maMethod = MODE_SMA, int appliedPrice = PRICE_CLOSE)
    : BaseIndicator("BollingerBands", symbol, timeframe, period)
{
    m_period = MathMax(1, period);
    m_deviation = MathMax(0.1, deviation);
    m_maMethod = maMethod;
    m_appliedPrice = appliedPrice;
    
    m_upperBand = 0.0;
    m_lowerBand = 0.0;
    m_middleBand = 0.0;
    m_bandwidth = 0.0;
    m_percentB = 0.0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
BollingerBands::~BollingerBands()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize Bollinger Bands                                       |
//+------------------------------------------------------------------+
bool BollingerBands::OnInitialize()
{
    if (!BaseIndicator::OnInitialize())
        return false;
    
    if (m_period < 2)
    {
        SetError(301, "Bollinger Bands period must be at least 2");
        return false;
    }
    
    if (m_deviation <= 0.0)
    {
        SetError(302, "Bollinger Bands deviation must be positive");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate Bollinger Bands parameters                             |
//+------------------------------------------------------------------+
bool BollingerBands::OnValidate()
{
    if (!BaseIndicator::OnValidate())
        return false;
    
    if (!IsDataAvailable(m_period))
    {
        SetError(303, "Insufficient data for Bollinger Bands calculation");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate Bollinger Bands value                                 |
//+------------------------------------------------------------------+
double BollingerBands::Calculate(int shift = 0)
{
    if (!IsValidShift(shift))
        return EMPTY_VALUE;
    
    // Calculate middle band (moving average)
    m_middleBand = iMA(GetSymbol(), GetTimeframe(), m_period, 0, m_maMethod, m_appliedPrice, shift);
    if (m_middleBand == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    // Calculate standard deviation
    double sum = 0.0;
    for (int i = 0; i < m_period; i++)
    {
        double price = GetPrice(m_appliedPrice, shift + i);
        if (price == EMPTY_VALUE)
            return EMPTY_VALUE;
        
        double diff = price - m_middleBand;
        sum += diff * diff;
    }
    
    double stdDev = MathSqrt(sum / m_period);
    
    // Calculate upper and lower bands
    m_upperBand = m_middleBand + (m_deviation * stdDev);
    m_lowerBand = m_middleBand - (m_deviation * stdDev);
    
    // Calculate bandwidth
    m_bandwidth = (m_upperBand - m_lowerBand) / m_middleBand * 100.0;
    
    // Calculate %B
    double currentPrice = GetPrice(m_appliedPrice, shift);
    if (currentPrice != EMPTY_VALUE && m_upperBand != m_lowerBand)
    {
        m_percentB = (currentPrice - m_lowerBand) / (m_upperBand - m_lowerBand);
    }
    else
    {
        m_percentB = 0.5; // Neutral position
    }
    
    return m_middleBand;
}

//+------------------------------------------------------------------+
//| Generate trading signal                                          |
//+------------------------------------------------------------------+
SignalInfo BollingerBands::GenerateSignal(int shift = 0)
{
    SignalInfo signal;
    signal.type = SIGNAL_NONE;
    signal.strength = STRENGTH_WEAK;
    signal.confidence = 0.0;
    signal.timestamp = GetTime(shift);
    signal.value = Calculate(shift);
    signal.description = "";
    
    if (signal.value == EMPTY_VALUE)
        return signal;
    
    double currentPrice = GetPrice(m_appliedPrice, shift);
    if (currentPrice == EMPTY_VALUE)
        return signal;
    
    // Generate signals based on Bollinger Bands
    if (IsPriceBelowLowerBand(shift))
    {
        // Price below lower band - potential buy signal (oversold)
        signal.type = SIGNAL_BUY;
        signal.strength = STRENGTH_MEDIUM;
        signal.confidence = MathMin(0.8, (m_lowerBand - currentPrice) / (m_upperBand - m_lowerBand) + 0.5);
        signal.description = "Price below lower Bollinger Band - Oversold condition";
    }
    else if (IsPriceAboveUpperBand(shift))
    {
        // Price above upper band - potential sell signal (overbought)
        signal.type = SIGNAL_SELL;
        signal.strength = STRENGTH_MEDIUM;
        signal.confidence = MathMin(0.8, (currentPrice - m_upperBand) / (m_upperBand - m_lowerBand) + 0.5);
        signal.description = "Price above upper Bollinger Band - Overbought condition";
    }
    else if (IsPriceNearLowerBand(shift, 0.05))
    {
        // Price near lower band - weak buy signal
        signal.type = SIGNAL_BUY;
        signal.strength = STRENGTH_WEAK;
        signal.confidence = 0.4;
        signal.description = "Price near lower Bollinger Band";
    }
    else if (IsPriceNearUpperBand(shift, 0.05))
    {
        // Price near upper band - weak sell signal
        signal.type = SIGNAL_SELL;
        signal.strength = STRENGTH_WEAK;
        signal.confidence = 0.4;
        signal.description = "Price near upper Bollinger Band";
    }
    
    // Adjust confidence based on bandwidth
    if (IsBandwidthExpanding(shift))
    {
        signal.confidence *= 1.2; // Increase confidence during expansion
    }
    else if (IsBandwidthContracting(shift))
    {
        signal.confidence *= 0.8; // Decrease confidence during contraction
    }
    
    signal.confidence = MathMax(0.0, MathMin(1.0, signal.confidence));
    
    return signal;
}

//+------------------------------------------------------------------+
//| Get upper band value                                             |
//+------------------------------------------------------------------+
double BollingerBands::GetUpperBand(int shift = 0) const
{
    return iBands(GetSymbol(), GetTimeframe(), m_period, m_deviation, 0, m_appliedPrice, MODE_UPPER, shift);
}

//+------------------------------------------------------------------+
//| Get lower band value                                             |
//+------------------------------------------------------------------+
double BollingerBands::GetLowerBand(int shift = 0) const
{
    return iBands(GetSymbol(), GetTimeframe(), m_period, m_deviation, 0, m_appliedPrice, MODE_LOWER, shift);
}

//+------------------------------------------------------------------+
//| Get middle band value                                            |
//+------------------------------------------------------------------+
double BollingerBands::GetMiddleBand(int shift = 0) const
{
    return iBands(GetSymbol(), GetTimeframe(), m_period, m_deviation, 0, m_appliedPrice, MODE_MAIN, shift);
}

//+------------------------------------------------------------------+
//| Get bandwidth                                                    |
//+------------------------------------------------------------------+
double BollingerBands::GetBandwidth(int shift = 0) const
{
    double upper = GetUpperBand(shift);
    double lower = GetLowerBand(shift);
    double middle = GetMiddleBand(shift);
    
    if (upper == EMPTY_VALUE || lower == EMPTY_VALUE || middle == EMPTY_VALUE || middle == 0.0)
        return EMPTY_VALUE;
    
    return (upper - lower) / middle * 100.0;
}

//+------------------------------------------------------------------+
//| Get %B value                                                     |
//+------------------------------------------------------------------+
double BollingerBands::GetPercentB(int shift = 0) const
{
    double upper = GetUpperBand(shift);
    double lower = GetLowerBand(shift);
    double price = GetPrice(m_appliedPrice, shift);
    
    if (upper == EMPTY_VALUE || lower == EMPTY_VALUE || price == EMPTY_VALUE || upper == lower)
        return EMPTY_VALUE;
    
    return (price - lower) / (upper - lower);
}

//+------------------------------------------------------------------+
//| Check if price is above upper band                              |
//+------------------------------------------------------------------+
bool BollingerBands::IsPriceAboveUpperBand(int shift = 0) const
{
    double price = GetPrice(m_appliedPrice, shift);
    double upper = GetUpperBand(shift);
    return (price != EMPTY_VALUE && upper != EMPTY_VALUE && price > upper);
}

//+------------------------------------------------------------------+
//| Check if price is below lower band                              |
//+------------------------------------------------------------------+
bool BollingerBands::IsPriceBelowLowerBand(int shift = 0) const
{
    double price = GetPrice(m_appliedPrice, shift);
    double lower = GetLowerBand(shift);
    return (price != EMPTY_VALUE && lower != EMPTY_VALUE && price < lower);
}

//+------------------------------------------------------------------+
//| Check if price is near upper band                               |
//+------------------------------------------------------------------+
bool BollingerBands::IsPriceNearUpperBand(int shift = 0, double threshold = 0.1) const
{
    double percentB = GetPercentB(shift);
    return (percentB != EMPTY_VALUE && percentB > (1.0 - threshold));
}

//+------------------------------------------------------------------+
//| Check if price is near lower band                               |
//+------------------------------------------------------------------+
bool BollingerBands::IsPriceNearLowerBand(int shift = 0, double threshold = 0.1) const
{
    double percentB = GetPercentB(shift);
    return (percentB != EMPTY_VALUE && percentB < threshold);
}

//+------------------------------------------------------------------+
//| Check if bandwidth is expanding                                 |
//+------------------------------------------------------------------+
bool BollingerBands::IsBandwidthExpanding(int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double currentBW = GetBandwidth(shift);
    double previousBW = GetBandwidth(shift + 1);
    
    return (currentBW != EMPTY_VALUE && previousBW != EMPTY_VALUE && currentBW > previousBW);
}

//+------------------------------------------------------------------+
//| Check if bandwidth is contracting                               |
//+------------------------------------------------------------------+
bool BollingerBands::IsBandwidthContracting(int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double currentBW = GetBandwidth(shift);
    double previousBW = GetBandwidth(shift + 1);
    
    return (currentBW != EMPTY_VALUE && previousBW != EMPTY_VALUE && currentBW < previousBW);
}

#endif // BOLLINGER_BANDS_MQH
