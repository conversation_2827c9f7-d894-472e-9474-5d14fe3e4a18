//+------------------------------------------------------------------+
//|                                          AccountProtection.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_MQH
#define ACCOUNT_PROTECTION_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"
#include "AccountProtectionValidator.mqh"
#include "AccountProtectionConfig.mqh"
#include "AccountDataProvider.mqh"
#include "AccountMonitor.mqh"
#include "RiskChecker.mqh"
#include "ProtectionStatusManager.mqh"
#include "TradingController.mqh"

//+------------------------------------------------------------------+
//| AccountProtection Class                                          |
//| Refactored to follow Single Responsibility Principle            |
//| Main responsibility: Coordinate protection components            |
//+------------------------------------------------------------------+
class AccountProtection : public BaseComponent
{
private:
    // Component composition (following SRP)
    AccountProtectionConfig*    m_config;           // Configuration management
    AccountDataProvider*        m_dataProvider;     // Data retrieval
    AccountMonitor*            m_monitor;           // Account monitoring
    RiskChecker*               m_riskChecker;       // Risk assessment
    ProtectionStatusManager*   m_statusManager;     // Status management
    TradingController*         m_tradingController; // Trading control

    // Validator component (existing)
    AccountProtectionValidator m_validator;         // Parameter validator

    // Component management
    bool                       m_componentsOwned;   // Whether we own the components

public:
    //--- Constructor and Destructor
                            AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE,
                                            bool strictMode = true,
                                            string symbol = "");
    virtual                ~AccountProtection();

    //--- Component access methods (for advanced usage)
    AccountProtectionConfig*    GetConfig() const { return m_config; }
    AccountDataProvider*        GetDataProvider() const { return m_dataProvider; }
    AccountMonitor*            GetMonitor() const { return m_monitor; }
    RiskChecker*               GetRiskChecker() const { return m_riskChecker; }
    ProtectionStatusManager*   GetStatusManager() const { return m_statusManager; }
    TradingController*         GetTradingController() const { return m_tradingController; }

    //--- Configuration methods (delegated to config component)
    void                    SetProtectionLevel(ENUM_PROTECTION_LEVEL level);
    void                    SetMaxLossPercent(double percent);
    void                    SetMaxDailyLoss(double amount);
    void                    SetMaxDrawdownPercent(double percent);
    void                    SetMaxOpenOrders(int count);
    void                    SetMaxLotSize(double lotSize);
    void                    SetMaxTotalLotSize(double totalLotSize);
    void                    SetMaxSpread(double spread);

    //--- Information methods (delegated to appropriate components)
    ENUM_PROTECTION_LEVEL   GetProtectionLevel() const;
    ENUM_PROTECTION_STATUS  GetCurrentStatus() const;
    double                  GetMaxLossPercent() const;
    double                  GetCurrentLossPercent() const;
    double                  GetCurrentDrawdownPercent() const;
    bool                    IsTradingHalted() const;

    //--- Protection check methods (delegated to risk checker and trading controller)
    bool                    IsTradingAllowed();
    bool                    IsNewOrderAllowed(double lotSize = 0.0, string symbol = "");
    bool                    IsSpreadAcceptable(string symbol = "");
    bool                    CheckAccountLimits();
    bool                    CheckPositionLimits(double lotSize = 0.0);
    bool                    CheckDailyLimits();

    //--- Status update methods (delegated to status manager and trading controller)
    void                    UpdateStatus();
    void                    ResetDailyCounters();
    void                    HaltTrading(string reason = "");
    void                    ResumeTrading();
    void                    EmergencyStop(string reason = "");

    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;

    //--- Utility methods (delegated to data provider)
    double                  GetAccountBalance() const;
    double                  GetAccountEquity() const;
    double                  GetAccountProfit() const;
    int                     GetOpenOrdersCount() const;
    double                  GetTotalLotSize() const;
    double                  GetCurrentSpread(string symbol = "") const;
    bool                    IsNewTradingDay() const;
    string                  GetStatusDescription() const;

    //--- Summary methods
    string                  GetProtectionSummary() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountProtection::AccountProtection(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE,
                                   bool strictMode = true,
                                   string symbol = "") : BaseComponent("AccountProtection")
{
    // Initialize validator component
    m_validator.SetStrictMode(strictMode);

    // Create and initialize components
    m_config = new AccountProtectionConfig(level);
    m_dataProvider = new AccountDataProvider((symbol == "") ? Symbol() : symbol);
    m_monitor = new AccountMonitor(m_dataProvider);
    m_riskChecker = new RiskChecker(m_config, m_monitor, m_dataProvider);
    m_statusManager = new ProtectionStatusManager(m_config, m_monitor);
    m_tradingController = new TradingController(m_riskChecker, m_statusManager);

    m_componentsOwned = true;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtection::~AccountProtection()
{
    // Cleanup components if we own them
    if (m_componentsOwned)
    {
        if (m_tradingController != NULL) delete m_tradingController;
        if (m_statusManager != NULL) delete m_statusManager;
        if (m_riskChecker != NULL) delete m_riskChecker;
        if (m_monitor != NULL) delete m_monitor;
        if (m_dataProvider != NULL) delete m_dataProvider;
        if (m_config != NULL) delete m_config;
    }
}

//+------------------------------------------------------------------+
//| Initialize account protection                                    |
//+------------------------------------------------------------------+
bool AccountProtection::OnInitialize()
{
    // Initialize all components in proper order
    if (!m_config.Initialize())
    {
        SetError(901, "Failed to initialize configuration component");
        return false;
    }

    if (!m_dataProvider.Initialize())
    {
        SetError(902, "Failed to initialize data provider component");
        return false;
    }

    if (!m_monitor.Initialize())
    {
        SetError(903, "Failed to initialize monitor component");
        return false;
    }

    if (!m_riskChecker.Initialize())
    {
        SetError(904, "Failed to initialize risk checker component");
        return false;
    }

    if (!m_statusManager.Initialize())
    {
        SetError(905, "Failed to initialize status manager component");
        return false;
    }

    if (!m_tradingController.Initialize())
    {
        SetError(906, "Failed to initialize trading controller component");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Validate parameters                                              |
//+------------------------------------------------------------------+
bool AccountProtection::OnValidate()
{
    // Validate all components
    if (!m_config.Validate())
    {
        SetError(907, "Configuration validation failed");
        return false;
    }

    if (!m_dataProvider.Validate())
    {
        SetError(908, "Data provider validation failed");
        return false;
    }

    if (!m_monitor.Validate())
    {
        SetError(909, "Monitor validation failed");
        return false;
    }

    if (!m_riskChecker.Validate())
    {
        SetError(910, "Risk checker validation failed");
        return false;
    }

    if (!m_statusManager.Validate())
    {
        SetError(911, "Status manager validation failed");
        return false;
    }

    if (!m_tradingController.Validate())
    {
        SetError(912, "Trading controller validation failed");
        return false;
    }

    // Validate using the existing validator for comprehensive checks
    bool validationResult = m_validator.ValidateAllProtectionParameters(
        m_config.GetMaxLossPercent(),
        m_config.GetMaxDrawdownPercent(),
        m_config.GetMaxOpenOrders(),
        m_config.GetMaxLotSize(),
        m_config.GetMaxTotalLotSize(),
        m_config.GetMaxSpread(),
        (int)m_config.GetProtectionLevel()
    );

    if (!validationResult)
    {
        SetError(913, "Parameter validation failed - check validator logs for details");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update protection status                                         |
//+------------------------------------------------------------------+
bool AccountProtection::OnUpdate()
{
    // Update all components
    if (!m_dataProvider.Update())
    {
        SetError(914, "Data provider update failed");
        return false;
    }

    if (!m_monitor.Update())
    {
        SetError(915, "Monitor update failed");
        return false;
    }

    if (!m_riskChecker.Update())
    {
        SetError(916, "Risk checker update failed");
        return false;
    }

    if (!m_statusManager.Update())
    {
        SetError(917, "Status manager update failed");
        return false;
    }

    if (!m_tradingController.Update())
    {
        SetError(918, "Trading controller update failed");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| Configuration methods (delegated to config component)           |
//+------------------------------------------------------------------+
void AccountProtection::SetProtectionLevel(ENUM_PROTECTION_LEVEL level)
{
    if (m_config != NULL)
        m_config.SetProtectionLevel(level);
}

void AccountProtection::SetMaxLossPercent(double percent)
{
    if (m_config != NULL)
        m_config.SetMaxLossPercent(percent);
}

void AccountProtection::SetMaxDailyLoss(double amount)
{
    if (m_config != NULL)
        m_config.SetMaxDailyLoss(amount);
}

void AccountProtection::SetMaxDrawdownPercent(double percent)
{
    if (m_config != NULL)
        m_config.SetMaxDrawdownPercent(percent);
}

void AccountProtection::SetMaxOpenOrders(int count)
{
    if (m_config != NULL)
        m_config.SetMaxOpenOrders(count);
}

void AccountProtection::SetMaxLotSize(double lotSize)
{
    if (m_config != NULL)
        m_config.SetMaxLotSize(lotSize);
}

void AccountProtection::SetMaxTotalLotSize(double totalLotSize)
{
    if (m_config != NULL)
        m_config.SetMaxTotalLotSize(totalLotSize);
}

void AccountProtection::SetMaxSpread(double spread)
{
    if (m_config != NULL)
        m_config.SetMaxSpread(spread);
}

//+------------------------------------------------------------------+
//| Information methods (delegated to appropriate components)        |
//+------------------------------------------------------------------+
ENUM_PROTECTION_LEVEL AccountProtection::GetProtectionLevel() const
{
    return (m_config != NULL) ? m_config.GetProtectionLevel() : PROTECTION_MODERATE;
}

ENUM_PROTECTION_STATUS AccountProtection::GetCurrentStatus() const
{
    return (m_statusManager != NULL) ? m_statusManager.GetCurrentStatus() : STATUS_NORMAL;
}

double AccountProtection::GetMaxLossPercent() const
{
    return (m_config != NULL) ? m_config.GetMaxLossPercent() : 20.0;
}

double AccountProtection::GetCurrentLossPercent() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentLossPercent() : 0.0;
}

double AccountProtection::GetCurrentDrawdownPercent() const
{
    return (m_monitor != NULL) ? m_monitor.GetCurrentDrawdownPercent() : 0.0;
}

bool AccountProtection::IsTradingHalted() const
{
    return (m_tradingController != NULL) ? m_tradingController.IsTradingHalted() : false;
}

//+------------------------------------------------------------------+
//| Protection check methods (delegated to components)              |
//+------------------------------------------------------------------+
bool AccountProtection::IsTradingAllowed()
{
    return (m_tradingController != NULL) ? m_tradingController.IsTradingAllowed() : false;
}

bool AccountProtection::IsNewOrderAllowed(double lotSize = 0.0, string symbol = "")
{
    return (m_tradingController != NULL) ? m_tradingController.IsNewOrderAllowed(lotSize, symbol) : false;
}

bool AccountProtection::IsSpreadAcceptable(string symbol = "")
{
    return (m_riskChecker != NULL) ? m_riskChecker.CheckSpreadLimits(symbol) : false;
}

bool AccountProtection::CheckAccountLimits()
{
    return (m_riskChecker != NULL) ? m_riskChecker.CheckAccountLimits() : false;
}

bool AccountProtection::CheckPositionLimits(double lotSize = 0.0)
{
    return (m_riskChecker != NULL) ? m_riskChecker.CheckPositionLimits(lotSize) : false;
}

bool AccountProtection::CheckDailyLimits()
{
    return (m_riskChecker != NULL) ? m_riskChecker.CheckDailyLimits() : false;
}

//+------------------------------------------------------------------+
//| Status update methods (delegated to components)                 |
//+------------------------------------------------------------------+
void AccountProtection::UpdateStatus()
{
    if (m_statusManager != NULL)
        m_statusManager.UpdateStatus();
}

void AccountProtection::ResetDailyCounters()
{
    if (m_monitor != NULL)
        m_monitor.ResetDailyCounters();
}

void AccountProtection::HaltTrading(string reason = "")
{
    if (m_tradingController != NULL)
        m_tradingController.HaltTrading(reason);
}

void AccountProtection::ResumeTrading()
{
    if (m_tradingController != NULL)
        m_tradingController.ResumeTrading();
}

void AccountProtection::EmergencyStop(string reason = "")
{
    if (m_tradingController != NULL)
        m_tradingController.EmergencyStop(reason);
}

//+------------------------------------------------------------------+
//| Utility methods (delegated to data provider)                    |
//+------------------------------------------------------------------+
double AccountProtection::GetAccountBalance() const
{
    return (m_dataProvider != NULL) ? m_dataProvider.GetAccountBalance() : 0.0;
}

double AccountProtection::GetAccountEquity() const
{
    return (m_dataProvider != NULL) ? m_dataProvider.GetAccountEquity() : 0.0;
}

double AccountProtection::GetAccountProfit() const
{
    return (m_dataProvider != NULL) ? m_dataProvider.GetAccountProfit() : 0.0;
}

int AccountProtection::GetOpenOrdersCount() const
{
    return (m_dataProvider != NULL) ? m_dataProvider.GetOpenOrdersCount() : 0;
}

double AccountProtection::GetTotalLotSize() const
{
    return (m_dataProvider != NULL) ? m_dataProvider.GetTotalLotSize() : 0.0;
}

double AccountProtection::GetCurrentSpread(string symbol = "") const
{
    return (m_dataProvider != NULL) ? m_dataProvider.GetCurrentSpread(symbol) : 0.0;
}

bool AccountProtection::IsNewTradingDay() const
{
    return (m_monitor != NULL) ? m_monitor.IsNewTradingDay() : false;
}

string AccountProtection::GetStatusDescription() const
{
    return (m_statusManager != NULL) ? m_statusManager.GetStatusDescription() : "Unknown";
}

//+------------------------------------------------------------------+
//| Get comprehensive protection summary                             |
//+------------------------------------------------------------------+
string AccountProtection::GetProtectionSummary() const
{
    string summary = "=== Account Protection Summary ===\n";

    if (m_config != NULL)
    {
        summary += "Configuration:\n" + m_config.GetConfigSummary() + "\n\n";
    }

    if (m_monitor != NULL)
    {
        summary += "Monitoring:\n" + m_monitor.GetMonitoringSummary() + "\n\n";
    }

    if (m_statusManager != NULL)
    {
        summary += "Status:\n" + m_statusManager.GetStatusSummary() + "\n\n";
    }

    if (m_riskChecker != NULL)
    {
        summary += "Risk Assessment:\n" + m_riskChecker.GetRiskSummary() + "\n\n";
    }

    if (m_tradingController != NULL)
    {
        summary += "Trading Control:\n" + m_tradingController.GetControlSummary() + "\n\n";
    }

    if (m_dataProvider != NULL)
    {
        summary += "Market Data:\n" + m_dataProvider.GetDataSummary();
    }

    return summary;
}

#endif // ACCOUNT_PROTECTION_MQH
