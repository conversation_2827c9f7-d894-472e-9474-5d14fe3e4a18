//+------------------------------------------------------------------+
//|                                            Example_Simplified.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef EXAMPLE_SIMPLIFIED_MQH
#define EXAMPLE_SIMPLIFIED_MQH

#include "AccountProtectionSimplified.mqh"

//+------------------------------------------------------------------+
//| Example: Simplified AccountProtection Usage                     |
//+------------------------------------------------------------------+
void ExampleSimplifiedUsage()
{
    Print("=== Simplified AccountProtection Example ===");
    
    // Create simplified AccountProtection (only 3 components)
    AccountProtection* protection = new AccountProtection(PROTECTION_MODERATE, true, Symbol());
    
    // Initialize
    if (!protection.Initialize())
    {
        Print("Failed to initialize: ", protection.GetLastErrorMessage());
        delete protection;
        return;
    }
    
    // Configure settings
    protection.SetMaxLossPercent(15.0);
    protection.SetMaxDrawdownPercent(25.0);
    protection.SetMaxOpenOrders(5);
    
    // Check trading permissions
    if (protection.IsTradingAllowed())
    {
        Print("Trading allowed");
        
        if (protection.IsNewOrderAllowed(0.1))
        {
            Print("Can open 0.1 lot order");
        }
    }
    else
    {
        Print("Trading not allowed: ", protection.GetStatusDescription());
    }
    
    // Get status
    Print("Status: ", protection.GetStatusDescription());
    Print("Loss: ", DoubleToString(protection.GetCurrentLossPercent(), 2), "%");
    Print("Drawdown: ", DoubleToString(protection.GetCurrentDrawdownPercent(), 2), "%");
    
    // Get summary
    Print("\n", protection.GetProtectionSummary());
    
    delete protection;
}

//+------------------------------------------------------------------+
//| Example: Component Access                                        |
//+------------------------------------------------------------------+
void ExampleComponentAccess()
{
    Print("=== Component Access Example ===");
    
    AccountProtection* protection = new AccountProtection(PROTECTION_STRICT);
    
    if (!protection.Initialize())
    {
        delete protection;
        return;
    }
    
    // Access individual components
    AccountProtectionConfig* config = protection.GetConfig();
    AccountMonitor* monitor = protection.GetMonitor();
    RiskController* riskController = protection.GetRiskController();
    
    // Configure monitor thresholds
    if (monitor != NULL)
    {
        monitor.SetWarningThreshold(40.0);
        monitor.SetCriticalThreshold(60.0);
        monitor.SetEmergencyThreshold(80.0);
    }
    
    // Perform risk checks
    if (riskController != NULL)
    {
        bool accountOK = riskController.CheckAccountLimits();
        bool positionOK = riskController.CheckPositionLimits(0.2);
        bool dailyOK = riskController.CheckDailyLimits();
        
        Print("Risk Checks:");
        Print("  Account: ", (accountOK ? "OK" : "FAILED"));
        Print("  Position: ", (positionOK ? "OK" : "FAILED"));
        Print("  Daily: ", (dailyOK ? "OK" : "FAILED"));
    }
    
    // Manual control
    if (riskController != NULL)
    {
        riskController.HaltTrading("Manual halt for testing");
        Print("Trading halted: ", riskController.IsTradingHalted());
        
        riskController.ResumeTrading();
        Print("Trading resumed: ", !riskController.IsTradingHalted());
    }
    
    delete protection;
}

//+------------------------------------------------------------------+
//| Example: EA Integration                                          |
//+------------------------------------------------------------------+
AccountProtection* g_protection = NULL;

bool InitializeSimplifiedProtection()
{
    g_protection = new AccountProtection(PROTECTION_MODERATE);
    
    if (!g_protection.Initialize())
    {
        Print("Protection init failed: ", g_protection.GetLastErrorMessage());
        delete g_protection;
        g_protection = NULL;
        return false;
    }
    
    // Configure for EA
    g_protection.SetMaxLossPercent(20.0);
    g_protection.SetMaxDrawdownPercent(30.0);
    g_protection.SetMaxOpenOrders(3);
    
    Print("Simplified protection system ready");
    return true;
}

void UpdateSimplifiedProtection()
{
    if (g_protection != NULL)
    {
        g_protection.Update();
        
        // Log status changes
        AccountMonitor* monitor = g_protection.GetMonitor();
        if (monitor != NULL && monitor.HasStatusChanged())
        {
            Print("Status changed to: ", g_protection.GetStatusDescription());
        }
    }
}

bool CanOpenTrade(double lotSize)
{
    return (g_protection != NULL) ? g_protection.IsNewOrderAllowed(lotSize) : false;
}

void CleanupSimplifiedProtection()
{
    if (g_protection != NULL)
    {
        delete g_protection;
        g_protection = NULL;
        Print("Simplified protection cleaned up");
    }
}

//+------------------------------------------------------------------+
//| Comparison: Original vs Simplified                              |
//+------------------------------------------------------------------+
void CompareVersions()
{
    Print("=== Version Comparison ===");
    Print("Original AccountProtection:");
    Print("  - Single class with multiple responsibilities");
    Print("  - ~540 lines of code");
    Print("  - Violates SRP but compact");
    Print("");
    
    Print("Full Refactored Version:");
    Print("  - 7 separate components");
    Print("  - ~2,325 lines of code (4.3x increase)");
    Print("  - Strict SRP compliance");
    Print("  - High maintainability but complex");
    Print("");
    
    Print("Simplified Refactored Version:");
    Print("  - 3 core components");
    Print("  - ~1,200 lines of code (2.2x increase)");
    Print("  - SRP compliance with practical balance");
    Print("  - Good maintainability and reasonable complexity");
    Print("");
    
    Print("Recommendation: Use Simplified Version for most projects");
}

//+------------------------------------------------------------------+
//| Performance Test                                                 |
//+------------------------------------------------------------------+
void PerformanceTest()
{
    Print("=== Performance Test ===");
    
    datetime startTime = GetTickCount();
    
    AccountProtection* protection = new AccountProtection(PROTECTION_MODERATE);
    protection.Initialize();
    
    // Simulate 1000 trading checks
    for (int i = 0; i < 1000; i++)
    {
        protection.Update();
        protection.IsTradingAllowed();
        protection.IsNewOrderAllowed(0.1);
        protection.CheckAccountLimits();
    }
    
    datetime endTime = GetTickCount();
    
    Print("1000 operations completed in: ", (endTime - startTime), " ms");
    Print("Average per operation: ", DoubleToString((endTime - startTime) / 1000.0, 2), " ms");
    
    delete protection;
}

//+------------------------------------------------------------------+
//| Usage in EA Main Functions (Simplified)                         |
//+------------------------------------------------------------------+
/*
// In your EA's OnInit()
int OnInit()
{
    if (!InitializeSimplifiedProtection())
        return INIT_FAILED;
    
    return INIT_SUCCEEDED;
}

// In your EA's OnTick()
void OnTick()
{
    UpdateSimplifiedProtection();
    
    if (CanOpenTrade(0.1))
    {
        // Your trading logic
        Print("Can trade");
    }
}

// In your EA's OnDeinit()
void OnDeinit(const int reason)
{
    CleanupSimplifiedProtection();
}
*/

#endif // EXAMPLE_SIMPLIFIED_MQH
