//+------------------------------------------------------------------+
//|                                      AccountProtectionConfig.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_CONFIG_MQH
#define ACCOUNT_PROTECTION_CONFIG_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"

//+------------------------------------------------------------------+
//| AccountProtectionConfig Class                                    |
//| Responsible for managing account protection configuration        |
//| Single Responsibility: Configuration Management                  |
//+------------------------------------------------------------------+
class AccountProtectionConfig : public BaseComponent
{
private:
    ENUM_PROTECTION_LEVEL   m_protectionLevel;     // Protection level
    
    // Account limits
    double                  m_maxLossPercent;       // Maximum loss percentage
    double                  m_maxDailyLoss;         // Maximum daily loss
    double                  m_maxDrawdownPercent;   // Maximum drawdown percentage
    double                  m_minEquityPercent;     // Minimum equity percentage
    
    // Position limits
    int                     m_maxOpenOrders;        // Maximum open orders
    double                  m_maxLotSize;           // Maximum lot size per trade
    double                  m_maxTotalLotSize;      // Maximum total lot size
    double                  m_maxSpread;            // Maximum allowed spread
    
public:
    //--- Constructor and Destructor
                            AccountProtectionConfig(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE);
    virtual                ~AccountProtectionConfig();
    
    //--- Configuration methods
    void                    SetProtectionLevel(ENUM_PROTECTION_LEVEL level);
    void                    SetMaxLossPercent(double percent);
    void                    SetMaxDailyLoss(double amount);
    void                    SetMaxDrawdownPercent(double percent);
    void                    SetMaxOpenOrders(int count);
    void                    SetMaxLotSize(double lotSize);
    void                    SetMaxTotalLotSize(double totalLotSize);
    void                    SetMaxSpread(double spread);
    void                    SetMinEquityPercent(double percent);
    
    //--- Information methods
    ENUM_PROTECTION_LEVEL   GetProtectionLevel() const { return m_protectionLevel; }
    double                  GetMaxLossPercent() const { return m_maxLossPercent; }
    double                  GetMaxDailyLoss() const { return m_maxDailyLoss; }
    double                  GetMaxDrawdownPercent() const { return m_maxDrawdownPercent; }
    double                  GetMinEquityPercent() const { return m_minEquityPercent; }
    int                     GetMaxOpenOrders() const { return m_maxOpenOrders; }
    double                  GetMaxLotSize() const { return m_maxLotSize; }
    double                  GetMaxTotalLotSize() const { return m_maxTotalLotSize; }
    double                  GetMaxSpread() const { return m_maxSpread; }
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    
    //--- Utility methods
    void                    LoadDefaultsForLevel(ENUM_PROTECTION_LEVEL level);
    string                  GetConfigSummary() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountProtectionConfig::AccountProtectionConfig(ENUM_PROTECTION_LEVEL level = PROTECTION_MODERATE) 
    : BaseComponent("AccountProtectionConfig")
{
    m_protectionLevel = level;
    LoadDefaultsForLevel(level);
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtectionConfig::~AccountProtectionConfig()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize configuration                                         |
//+------------------------------------------------------------------+
bool AccountProtectionConfig::OnInitialize()
{
    // Configuration doesn't need special initialization
    return true;
}

//+------------------------------------------------------------------+
//| Validate configuration parameters                                |
//+------------------------------------------------------------------+
bool AccountProtectionConfig::OnValidate()
{
    if (m_maxLossPercent <= 0.0 || m_maxLossPercent > 50.0)
    {
        SetError(1001, "Invalid maximum loss percentage");
        return false;
    }
    
    if (m_maxDrawdownPercent <= 0.0 || m_maxDrawdownPercent > 80.0)
    {
        SetError(1002, "Invalid maximum drawdown percentage");
        return false;
    }
    
    if (m_maxOpenOrders <= 0)
    {
        SetError(1003, "Invalid maximum open orders count");
        return false;
    }
    
    if (m_maxLotSize <= 0.0)
    {
        SetError(1004, "Invalid maximum lot size");
        return false;
    }
    
    if (m_maxTotalLotSize < m_maxLotSize)
    {
        SetError(1005, "Total lot size cannot be less than max lot size");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Set protection level and load defaults                          |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetProtectionLevel(ENUM_PROTECTION_LEVEL level)
{
    m_protectionLevel = level;
    LoadDefaultsForLevel(level);
}

//+------------------------------------------------------------------+
//| Set maximum loss percentage                                      |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxLossPercent(double percent)
{
    m_maxLossPercent = MathMax(1.0, MathMin(50.0, percent));
    m_minEquityPercent = 100.0 - m_maxLossPercent;
}

//+------------------------------------------------------------------+
//| Set maximum daily loss                                           |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxDailyLoss(double amount)
{
    m_maxDailyLoss = MathMax(0.0, amount);
}

//+------------------------------------------------------------------+
//| Set maximum drawdown percentage                                  |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxDrawdownPercent(double percent)
{
    m_maxDrawdownPercent = MathMax(1.0, MathMin(80.0, percent));
}

//+------------------------------------------------------------------+
//| Set maximum open orders                                          |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxOpenOrders(int count)
{
    m_maxOpenOrders = MathMax(1, count);
}

//+------------------------------------------------------------------+
//| Set maximum lot size                                             |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxLotSize(double lotSize)
{
    m_maxLotSize = MathMax(0.01, lotSize);
}

//+------------------------------------------------------------------+
//| Set maximum total lot size                                       |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxTotalLotSize(double totalLotSize)
{
    m_maxTotalLotSize = MathMax(0.01, totalLotSize);
}

//+------------------------------------------------------------------+
//| Set maximum spread                                               |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMaxSpread(double spread)
{
    m_maxSpread = MathMax(0.0, spread);
}

//+------------------------------------------------------------------+
//| Set minimum equity percentage                                    |
//+------------------------------------------------------------------+
void AccountProtectionConfig::SetMinEquityPercent(double percent)
{
    m_minEquityPercent = MathMax(50.0, MathMin(99.0, percent));
}

//+------------------------------------------------------------------+
//| Load default values for protection level                        |
//+------------------------------------------------------------------+
void AccountProtectionConfig::LoadDefaultsForLevel(ENUM_PROTECTION_LEVEL level)
{
    switch(level)
    {
        case PROTECTION_NONE:
            m_maxLossPercent = 50.0;
            m_maxDrawdownPercent = 80.0;
            m_maxOpenOrders = 100;
            m_maxLotSize = 10.0;
            m_maxTotalLotSize = 50.0;
            m_maxSpread = 20.0;
            break;
            
        case PROTECTION_BASIC:
            m_maxLossPercent = 30.0;
            m_maxDrawdownPercent = 50.0;
            m_maxOpenOrders = 50;
            m_maxLotSize = 5.0;
            m_maxTotalLotSize = 20.0;
            m_maxSpread = 10.0;
            break;
            
        case PROTECTION_MODERATE:
            m_maxLossPercent = 20.0;
            m_maxDrawdownPercent = 30.0;
            m_maxOpenOrders = 20;
            m_maxLotSize = 2.0;
            m_maxTotalLotSize = 10.0;
            m_maxSpread = 5.0;
            break;
            
        case PROTECTION_STRICT:
            m_maxLossPercent = 10.0;
            m_maxDrawdownPercent = 15.0;
            m_maxOpenOrders = 10;
            m_maxLotSize = 1.0;
            m_maxTotalLotSize = 5.0;
            m_maxSpread = 3.0;
            break;
    }
    
    m_minEquityPercent = 100.0 - m_maxLossPercent;
    // Daily loss is typically half of max loss for conservative approach
    m_maxDailyLoss = 0.0; // Will be set based on account balance
}

//+------------------------------------------------------------------+
//| Get configuration summary                                        |
//+------------------------------------------------------------------+
string AccountProtectionConfig::GetConfigSummary() const
{
    string summary = "Protection Level: ";
    
    switch(m_protectionLevel)
    {
        case PROTECTION_NONE:     summary += "NONE"; break;
        case PROTECTION_BASIC:    summary += "BASIC"; break;
        case PROTECTION_MODERATE: summary += "MODERATE"; break;
        case PROTECTION_STRICT:   summary += "STRICT"; break;
    }
    
    summary += StringFormat("\nMax Loss: %.1f%%, Max Drawdown: %.1f%%", 
                           m_maxLossPercent, m_maxDrawdownPercent);
    summary += StringFormat("\nMax Orders: %d, Max Lot: %.2f, Max Total Lot: %.2f", 
                           m_maxOpenOrders, m_maxLotSize, m_maxTotalLotSize);
    summary += StringFormat("\nMax Spread: %.1f points", m_maxSpread);
    
    return summary;
}

#endif // ACCOUNT_PROTECTION_CONFIG_MQH
