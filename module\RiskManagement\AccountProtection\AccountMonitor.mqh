//+------------------------------------------------------------------+
//|                                            AccountMonitor.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_MONITOR_MQH
#define ACCOUNT_MONITOR_MQH

#include "../../Base/BaseComponent.mqh"
#include "AccountDataProvider.mqh"

//+------------------------------------------------------------------+
//| AccountMonitor Class                                             |
//| Responsible for monitoring account status and tracking metrics  |
//| Single Responsibility: Account Monitoring                       |
//+------------------------------------------------------------------+
class AccountMonitor : public BaseComponent
{
private:
    AccountDataProvider*    m_dataProvider;         // Data provider component
    
    // Monitoring variables
    double                  m_initialBalance;       // Initial account balance
    double                  m_dailyStartBalance;    // Balance at start of day
    double                  m_maxEquity;            // Maximum equity reached
    datetime                m_lastResetTime;        // Last daily reset time
    
    // Calculated metrics
    double                  m_currentLossPercent;   // Current loss percentage
    double                  m_currentDrawdownPercent; // Current drawdown percentage
    double                  m_dailyLoss;            // Daily loss amount
    double                  m_dailyProfit;          // Daily profit amount
    
public:
    //--- Constructor and Destructor
                            AccountMonitor(AccountDataProvider* dataProvider);
    virtual                ~AccountMonitor();
    
    //--- Monitoring methods
    void                    UpdateMetrics();
    void                    ResetDailyCounters();
    bool                    IsNewTradingDay() const;
    
    //--- Information methods
    double                  GetInitialBalance() const { return m_initialBalance; }
    double                  GetDailyStartBalance() const { return m_dailyStartBalance; }
    double                  GetMaxEquity() const { return m_maxEquity; }
    double                  GetCurrentLossPercent() const { return m_currentLossPercent; }
    double                  GetCurrentDrawdownPercent() const { return m_currentDrawdownPercent; }
    double                  GetDailyLoss() const { return m_dailyLoss; }
    double                  GetDailyProfit() const { return m_dailyProfit; }
    datetime                GetLastResetTime() const { return m_lastResetTime; }
    
    //--- Calculation methods
    double                  CalculateLossPercent() const;
    double                  CalculateDrawdownPercent() const;
    double                  CalculateDailyLoss() const;
    double                  CalculateDailyProfit() const;
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    string                  GetMonitoringSummary() const;
    void                    LogMetrics() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountMonitor::AccountMonitor(AccountDataProvider* dataProvider) : BaseComponent("AccountMonitor")
{
    m_dataProvider = dataProvider;
    m_initialBalance = 0.0;
    m_dailyStartBalance = 0.0;
    m_maxEquity = 0.0;
    m_lastResetTime = 0;
    m_currentLossPercent = 0.0;
    m_currentDrawdownPercent = 0.0;
    m_dailyLoss = 0.0;
    m_dailyProfit = 0.0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountMonitor::~AccountMonitor()
{
    // Data provider is managed externally
}

//+------------------------------------------------------------------+
//| Initialize account monitor                                       |
//+------------------------------------------------------------------+
bool AccountMonitor::OnInitialize()
{
    if (m_dataProvider == NULL)
    {
        SetError(3001, "Data provider not set");
        return false;
    }
    
    if (!m_dataProvider.IsInitialized())
    {
        SetError(3002, "Data provider not initialized");
        return false;
    }
    
    m_initialBalance = m_dataProvider.GetAccountBalance();
    m_dailyStartBalance = m_initialBalance;
    m_maxEquity = m_dataProvider.GetAccountEquity();
    m_lastResetTime = m_dataProvider.GetCurrentTime();
    
    if (m_initialBalance <= 0.0)
    {
        SetError(3003, "Invalid initial account balance");
        return false;
    }
    
    UpdateMetrics();
    return true;
}

//+------------------------------------------------------------------+
//| Validate monitor parameters                                      |
//+------------------------------------------------------------------+
bool AccountMonitor::OnValidate()
{
    if (m_dataProvider == NULL)
    {
        SetError(3004, "Data provider validation failed");
        return false;
    }
    
    if (m_initialBalance <= 0.0)
    {
        SetError(3005, "Invalid initial balance for monitoring");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update monitor                                                   |
//+------------------------------------------------------------------+
bool AccountMonitor::OnUpdate()
{
    // Check if new trading day
    if (IsNewTradingDay())
    {
        ResetDailyCounters();
    }
    
    UpdateMetrics();
    return true;
}

//+------------------------------------------------------------------+
//| Update all metrics                                               |
//+------------------------------------------------------------------+
void AccountMonitor::UpdateMetrics()
{
    if (m_dataProvider == NULL)
        return;
    
    // Update maximum equity
    double currentEquity = m_dataProvider.GetAccountEquity();
    if (currentEquity > m_maxEquity)
    {
        m_maxEquity = currentEquity;
    }
    
    // Calculate current metrics
    m_currentLossPercent = CalculateLossPercent();
    m_currentDrawdownPercent = CalculateDrawdownPercent();
    m_dailyLoss = CalculateDailyLoss();
    m_dailyProfit = CalculateDailyProfit();
}

//+------------------------------------------------------------------+
//| Reset daily counters                                             |
//+------------------------------------------------------------------+
void AccountMonitor::ResetDailyCounters()
{
    if (m_dataProvider == NULL)
        return;
    
    m_dailyStartBalance = m_dataProvider.GetAccountBalance();
    m_lastResetTime = m_dataProvider.GetCurrentTime();
    m_dailyLoss = 0.0;
    m_dailyProfit = 0.0;
}

//+------------------------------------------------------------------+
//| Check if new trading day                                         |
//+------------------------------------------------------------------+
bool AccountMonitor::IsNewTradingDay() const
{
    if (m_dataProvider == NULL)
        return false;
    
    datetime currentTime = m_dataProvider.GetCurrentTime();
    datetime lastTime = m_lastResetTime;
    
    return (TimeDay(currentTime) != TimeDay(lastTime) || 
            TimeMonth(currentTime) != TimeMonth(lastTime) ||
            TimeYear(currentTime) != TimeYear(lastTime));
}

//+------------------------------------------------------------------+
//| Calculate loss percentage                                        |
//+------------------------------------------------------------------+
double AccountMonitor::CalculateLossPercent() const
{
    if (m_dataProvider == NULL || m_initialBalance <= 0.0)
        return 0.0;
    
    double currentBalance = m_dataProvider.GetAccountBalance();
    double loss = m_initialBalance - currentBalance;
    return (loss / m_initialBalance) * 100.0;
}

//+------------------------------------------------------------------+
//| Calculate drawdown percentage                                    |
//+------------------------------------------------------------------+
double AccountMonitor::CalculateDrawdownPercent() const
{
    if (m_dataProvider == NULL || m_maxEquity <= 0.0)
        return 0.0;
    
    double currentEquity = m_dataProvider.GetAccountEquity();
    double drawdown = m_maxEquity - currentEquity;
    return (drawdown / m_maxEquity) * 100.0;
}

//+------------------------------------------------------------------+
//| Calculate daily loss                                             |
//+------------------------------------------------------------------+
double AccountMonitor::CalculateDailyLoss() const
{
    if (m_dataProvider == NULL)
        return 0.0;
    
    double currentBalance = m_dataProvider.GetAccountBalance();
    double loss = m_dailyStartBalance - currentBalance;
    return MathMax(0.0, loss);
}

//+------------------------------------------------------------------+
//| Calculate daily profit                                           |
//+------------------------------------------------------------------+
double AccountMonitor::CalculateDailyProfit() const
{
    if (m_dataProvider == NULL)
        return 0.0;
    
    double currentBalance = m_dataProvider.GetAccountBalance();
    double profit = currentBalance - m_dailyStartBalance;
    return MathMax(0.0, profit);
}

//+------------------------------------------------------------------+
//| Get monitoring summary                                           |
//+------------------------------------------------------------------+
string AccountMonitor::GetMonitoringSummary() const
{
    string summary = StringFormat("Initial Balance: %.2f, Current: %.2f", 
                                 m_initialBalance, 
                                 (m_dataProvider != NULL) ? m_dataProvider.GetAccountBalance() : 0.0);
    summary += StringFormat("\nLoss: %.2f%%, Drawdown: %.2f%%", 
                           m_currentLossPercent, m_currentDrawdownPercent);
    summary += StringFormat("\nDaily: Loss=%.2f, Profit=%.2f", 
                           m_dailyLoss, m_dailyProfit);
    summary += StringFormat("\nMax Equity: %.2f, Last Reset: %s", 
                           m_maxEquity, TimeToString(m_lastResetTime));
    
    return summary;
}

//+------------------------------------------------------------------+
//| Log current metrics                                              |
//+------------------------------------------------------------------+
void AccountMonitor::LogMetrics() const
{
    Print("AccountMonitor Metrics:");
    Print("  Loss: ", DoubleToString(m_currentLossPercent, 2), "%");
    Print("  Drawdown: ", DoubleToString(m_currentDrawdownPercent, 2), "%");
    Print("  Daily Loss: ", DoubleToString(m_dailyLoss, 2));
    Print("  Daily Profit: ", DoubleToString(m_dailyProfit, 2));
}

#endif // ACCOUNT_MONITOR_MQH
