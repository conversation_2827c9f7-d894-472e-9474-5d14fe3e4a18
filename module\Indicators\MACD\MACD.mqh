//+------------------------------------------------------------------+
//|                                                         MACD.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef MACD_MQH
#define MACD_MQH

#include "../../Base/BaseIndicator.mqh"

//+------------------------------------------------------------------+
//| MACD Class                                                       |
//| Implementation of MACD (Moving Average Convergence Divergence)  |
//+------------------------------------------------------------------+
class MACD : public BaseIndicator
{
private:
    int               m_fastEMA;          // Fast EMA period
    int               m_slowEMA;          // Slow EMA period
    int               m_signalSMA;        // Signal SMA period
    int               m_appliedPrice;     // Applied price type
    
    // MACD values
    double            m_macdMain;         // MACD main line
    double            m_macdSignal;       // MACD signal line
    double            m_macdHistogram;    // MACD histogram

public:
    //--- Constructor and Destructor
                      MACD(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                          int fastEMA = 12, int slowEMA = 26, int signalSMA = 9,
                          int appliedPrice = PRICE_CLOSE);
    virtual          ~MACD();
    
    //--- Configuration methods
    void              SetFastEMA(int period) { m_fastEMA = MathMax(1, period); }
    void              SetSlowEMA(int period) { m_slowEMA = MathMax(1, period); }
    void              SetSignalSMA(int period) { m_signalSMA = MathMax(1, period); }
    void              SetAppliedPrice(int price) { m_appliedPrice = price; }
    
    //--- Information methods
    int               GetFastEMA() const { return m_fastEMA; }
    int               GetSlowEMA() const { return m_slowEMA; }
    int               GetSignalSMA() const { return m_signalSMA; }
    int               GetAppliedPrice() const { return m_appliedPrice; }
    
    //--- MACD values
    double            GetMACDMain(int shift = 0) const;
    double            GetMACDSignal(int shift = 0) const;
    double            GetMACDHistogram(int shift = 0) const;
    
    //--- Override base class methods
    virtual double    Calculate(int shift = 0) override;
    virtual SignalInfo GenerateSignal(int shift = 0) override;
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Signal analysis methods
    bool              IsBullishCrossover(int shift = 0) const;
    bool              IsBearishCrossover(int shift = 0) const;
    bool              IsAboveZero(int shift = 0) const;
    bool              IsBelowZero(int shift = 0) const;
    bool              IsHistogramIncreasing(int shift = 0) const;
    bool              IsHistogramDecreasing(int shift = 0) const;
    bool              IsDivergence(int shift = 0, int lookback = 10) const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
MACD::MACD(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
          int fastEMA = 12, int slowEMA = 26, int signalSMA = 9,
          int appliedPrice = PRICE_CLOSE)
    : BaseIndicator("MACD", symbol, timeframe, MathMax(fastEMA, slowEMA))
{
    m_fastEMA = MathMax(1, fastEMA);
    m_slowEMA = MathMax(1, slowEMA);
    m_signalSMA = MathMax(1, signalSMA);
    m_appliedPrice = appliedPrice;
    
    m_macdMain = 0.0;
    m_macdSignal = 0.0;
    m_macdHistogram = 0.0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
MACD::~MACD()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize MACD                                                  |
//+------------------------------------------------------------------+
bool MACD::OnInitialize()
{
    if (!BaseIndicator::OnInitialize())
        return false;
    
    if (m_fastEMA >= m_slowEMA)
    {
        SetError(401, "Fast EMA period must be less than Slow EMA period");
        return false;
    }
    
    if (m_signalSMA < 1)
    {
        SetError(402, "Signal SMA period must be at least 1");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate MACD parameters                                         |
//+------------------------------------------------------------------+
bool MACD::OnValidate()
{
    if (!BaseIndicator::OnValidate())
        return false;
    
    if (!IsDataAvailable(m_slowEMA + m_signalSMA))
    {
        SetError(403, "Insufficient data for MACD calculation");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate MACD value                                             |
//+------------------------------------------------------------------+
double MACD::Calculate(int shift = 0)
{
    if (!IsValidShift(shift))
        return EMPTY_VALUE;
    
    // Get MACD values using built-in function
    m_macdMain = iMACD(GetSymbol(), GetTimeframe(), m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_MAIN, shift);
    m_macdSignal = iMACD(GetSymbol(), GetTimeframe(), m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_SIGNAL, shift);
    
    if (m_macdMain == EMPTY_VALUE || m_macdSignal == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    // Calculate histogram
    m_macdHistogram = m_macdMain - m_macdSignal;
    
    return m_macdMain;
}

//+------------------------------------------------------------------+
//| Generate trading signal                                          |
//+------------------------------------------------------------------+
SignalInfo MACD::GenerateSignal(int shift = 0)
{
    SignalInfo signal;
    signal.type = SIGNAL_NONE;
    signal.strength = STRENGTH_WEAK;
    signal.confidence = 0.0;
    signal.timestamp = GetTime(shift);
    signal.value = Calculate(shift);
    signal.description = "";
    
    if (signal.value == EMPTY_VALUE)
        return signal;
    
    // Check for crossover signals
    if (IsBullishCrossover(shift))
    {
        signal.type = SIGNAL_BUY;
        signal.strength = STRENGTH_MEDIUM;
        signal.confidence = 0.7;
        signal.description = "MACD Bullish Crossover";
        
        // Increase confidence if MACD is below zero (stronger signal)
        if (IsBelowZero(shift))
        {
            signal.confidence = 0.8;
            signal.strength = STRENGTH_STRONG;
            signal.description = "MACD Bullish Crossover from Below Zero";
        }
    }
    else if (IsBearishCrossover(shift))
    {
        signal.type = SIGNAL_SELL;
        signal.strength = STRENGTH_MEDIUM;
        signal.confidence = 0.7;
        signal.description = "MACD Bearish Crossover";
        
        // Increase confidence if MACD is above zero (stronger signal)
        if (IsAboveZero(shift))
        {
            signal.confidence = 0.8;
            signal.strength = STRENGTH_STRONG;
            signal.description = "MACD Bearish Crossover from Above Zero";
        }
    }
    else
    {
        // Check for histogram momentum signals
        if (IsAboveZero(shift) && IsHistogramIncreasing(shift))
        {
            signal.type = SIGNAL_BUY;
            signal.strength = STRENGTH_WEAK;
            signal.confidence = 0.4;
            signal.description = "MACD Above Zero with Increasing Histogram";
        }
        else if (IsBelowZero(shift) && IsHistogramDecreasing(shift))
        {
            signal.type = SIGNAL_SELL;
            signal.strength = STRENGTH_WEAK;
            signal.confidence = 0.4;
            signal.description = "MACD Below Zero with Decreasing Histogram";
        }
    }
    
    // Check for divergence (additional confirmation)
    if (IsDivergence(shift))
    {
        if (signal.type == SIGNAL_BUY)
        {
            signal.confidence *= 1.2;
            signal.description += " with Bullish Divergence";
        }
        else if (signal.type == SIGNAL_SELL)
        {
            signal.confidence *= 1.2;
            signal.description += " with Bearish Divergence";
        }
    }
    
    signal.confidence = MathMax(0.0, MathMin(1.0, signal.confidence));
    
    return signal;
}

//+------------------------------------------------------------------+
//| Get MACD main line value                                         |
//+------------------------------------------------------------------+
double MACD::GetMACDMain(int shift = 0) const
{
    return iMACD(GetSymbol(), GetTimeframe(), m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_MAIN, shift);
}

//+------------------------------------------------------------------+
//| Get MACD signal line value                                       |
//+------------------------------------------------------------------+
double MACD::GetMACDSignal(int shift = 0) const
{
    return iMACD(GetSymbol(), GetTimeframe(), m_fastEMA, m_slowEMA, m_signalSMA, m_appliedPrice, MODE_SIGNAL, shift);
}

//+------------------------------------------------------------------+
//| Get MACD histogram value                                         |
//+------------------------------------------------------------------+
double MACD::GetMACDHistogram(int shift = 0) const
{
    double main = GetMACDMain(shift);
    double signal = GetMACDSignal(shift);
    
    if (main == EMPTY_VALUE || signal == EMPTY_VALUE)
        return EMPTY_VALUE;
    
    return main - signal;
}

//+------------------------------------------------------------------+
//| Check for bullish crossover                                     |
//+------------------------------------------------------------------+
bool MACD::IsBullishCrossover(int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double currentMain = GetMACDMain(shift);
    double currentSignal = GetMACDSignal(shift);
    double previousMain = GetMACDMain(shift + 1);
    double previousSignal = GetMACDSignal(shift + 1);
    
    if (currentMain == EMPTY_VALUE || currentSignal == EMPTY_VALUE ||
        previousMain == EMPTY_VALUE || previousSignal == EMPTY_VALUE)
        return false;
    
    return (previousMain <= previousSignal && currentMain > currentSignal);
}

//+------------------------------------------------------------------+
//| Check for bearish crossover                                     |
//+------------------------------------------------------------------+
bool MACD::IsBearishCrossover(int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double currentMain = GetMACDMain(shift);
    double currentSignal = GetMACDSignal(shift);
    double previousMain = GetMACDMain(shift + 1);
    double previousSignal = GetMACDSignal(shift + 1);
    
    if (currentMain == EMPTY_VALUE || currentSignal == EMPTY_VALUE ||
        previousMain == EMPTY_VALUE || previousSignal == EMPTY_VALUE)
        return false;
    
    return (previousMain >= previousSignal && currentMain < currentSignal);
}

//+------------------------------------------------------------------+
//| Check if MACD is above zero                                     |
//+------------------------------------------------------------------+
bool MACD::IsAboveZero(int shift = 0) const
{
    double main = GetMACDMain(shift);
    return (main != EMPTY_VALUE && main > 0.0);
}

//+------------------------------------------------------------------+
//| Check if MACD is below zero                                     |
//+------------------------------------------------------------------+
bool MACD::IsBelowZero(int shift = 0) const
{
    double main = GetMACDMain(shift);
    return (main != EMPTY_VALUE && main < 0.0);
}

//+------------------------------------------------------------------+
//| Check if histogram is increasing                                |
//+------------------------------------------------------------------+
bool MACD::IsHistogramIncreasing(int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double currentHist = GetMACDHistogram(shift);
    double previousHist = GetMACDHistogram(shift + 1);
    
    return (currentHist != EMPTY_VALUE && previousHist != EMPTY_VALUE && currentHist > previousHist);
}

//+------------------------------------------------------------------+
//| Check if histogram is decreasing                                |
//+------------------------------------------------------------------+
bool MACD::IsHistogramDecreasing(int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double currentHist = GetMACDHistogram(shift);
    double previousHist = GetMACDHistogram(shift + 1);
    
    return (currentHist != EMPTY_VALUE && previousHist != EMPTY_VALUE && currentHist < previousHist);
}

//+------------------------------------------------------------------+
//| Check for divergence                                            |
//+------------------------------------------------------------------+
bool MACD::IsDivergence(int shift = 0, int lookback = 10) const
{
    if (shift + lookback >= GetBarsCount())
        return false;
    
    // Simple divergence detection
    // Compare price highs/lows with MACD highs/lows over lookback period
    
    double currentPrice = GetPrice(m_appliedPrice, shift);
    double currentMACD = GetMACDMain(shift);
    double pastPrice = GetPrice(m_appliedPrice, shift + lookback);
    double pastMACD = GetMACDMain(shift + lookback);
    
    if (currentPrice == EMPTY_VALUE || currentMACD == EMPTY_VALUE ||
        pastPrice == EMPTY_VALUE || pastMACD == EMPTY_VALUE)
        return false;
    
    // Bullish divergence: price makes lower low, MACD makes higher low
    bool bullishDiv = (currentPrice < pastPrice && currentMACD > pastMACD && currentMACD < 0);
    
    // Bearish divergence: price makes higher high, MACD makes lower high
    bool bearishDiv = (currentPrice > pastPrice && currentMACD < pastMACD && currentMACD > 0);
    
    return (bullishDiv || bearishDiv);
}

#endif // MACD_MQH
