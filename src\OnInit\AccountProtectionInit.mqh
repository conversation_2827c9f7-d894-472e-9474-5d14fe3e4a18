//+------------------------------------------------------------------+
//|                                        AccountProtectionInit.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_INIT_MQH
#define ACCOUNT_PROTECTION_INIT_MQH

#property strict

// Include EA_Wizard framework components
#include "../../../mql4_module/EA_Wizard/MainPipeline.mqh"

// Include account protection module
#include "../../module/RiskManagement/AccountProtection/AccountProtection.mqh"

// Include configuration module
#include "../Config/index.mqh"

//+------------------------------------------------------------------+
//| Account Protection Initialization Pipeline                       |
//| Implements account protection system initialization during      |
//| EA startup following EA_Wizard framework guidelines             |
//+------------------------------------------------------------------+
class AccountProtectionInit : public MainPipeline
{
private:
    // Protection Level Settings
    static const ENUM_PROTECTION_LEVEL ProtectionLevel;  // Account Protection Level

    static const double MaxLossPercent;          // Maximum Loss Percentage (0 = use protection level default)
    static const double MaxDailyLoss;             // Maximum Daily Loss Amount (0 = auto-calculate)
    static const double MaxDrawdownPercent;      // Maximum Drawdown Percentage (0 = use protection level default)

    // Position Limits
    static const int MaxOpenOrders;                // Maximum Open Orders (0 = use protection level default)
    static const double MaxLotSize;               // Maximum Lot Size per Trade (0 = use protection level default)
    static const double MaxTotalLotSize;         // Maximum Total Lot Size (0 = use protection level default)
    static const double MaxSpread;                // Maximum Allowed Spread (0 = use protection level default)

    // Account Protection Infomation
    static const string AccProtectionName;  // Account Protection Object Name
    static const string AccProtectionDesc;  // Account Protection Object Description

    // Monitoring Settings
    static const bool EnableAccountProtection;   // Enable Account Protection System
    static const bool LogProtectionEvents;       // Log Protection Events

    // Account Protection Instance
    AccountProtection* m_accountProtection;  // Account protection instance

public:
    //--- Constructor
    AccountProtectionInit() : MainPipeline(INIT_VARIABLES, "AccountProtectionInit")
    {
        m_accountProtection = new AccountProtection(InpProtectionLevel);
    }

    //--- Destructor
    ~AccountProtectionInit()
    {
        // Cleanup handled by framework
    }

protected:
    //--- Main initialization logic
    virtual void Main() override
    {
        Print("=== Account Protection Initialization Started ===");

        // Step 1: Configure custom parameters if specified
        ConfigureCustomParameters();

        // Step 2: Initialize account protection system
        if (!InitializeAccountProtection())
        {
            SetResult(false, "Failed to initialize account protection system");
            return;
        }
        
        // Step 3: Register account protection instance
        if (!RegisterAccountProtection())
        {
            SetResult(false, "Failed to register account protection instance");
            return;
        }
        
        // Step 4: Log successful initialization
        LogInitializationSuccess();
        SetResult(true, "Account protection system initialized successfully");
    }

private:
    //--- Helper methods
    void ConfigureCustomParameters();
    bool InitializeAccountProtection();
    bool RegisterAccountProtection();
    void LogInitializationSuccess();
};

//+------------------------------------------------------------------+
//| Account Protection Parameters                                   |
//| Self-contained module parameters following EA_Wizard guidelines |
//+------------------------------------------------------------------+

// Protection Level Settings
const ENUM_PROTECTION_LEVEL AccountProtectionInit::ProtectionLevel = PROTECTION_MODERATE;

const double AccountProtectionInit::MaxLossPercent = 20.0;
const double AccountProtectionInit::MaxDailyLoss = 0.0;
const double AccountProtectionInit::MaxDrawdownPercent = 30.0;

// Position Limits
const int AccountProtectionInit::MaxOpenOrders = 20;
const double AccountProtectionInit::MaxLotSize = 2.0;
const double AccountProtectionInit::MaxTotalLotSize = 10.0;
const double AccountProtectionInit::MaxSpread = 5.0;

// Account Protection Infomation
const string AccountProtectionInit::AccProtectionName = EA_OBJ_ACCOUNT_PROTECTION;
const string AccountProtectionInit::AccProtectionDesc = EA_OBJ_ACCOUNT_PROTECTION_DESC;

// Monitoring Settings
const bool AccountProtectionInit::EnableAccountProtection = EA_OBJ_ACCOUNT_PROTECTION_ENABLED;
const bool AccountProtectionInit::LogProtectionEvents = EA_OBJ_ACCOUNT_PROTECTION_LOG_EVENTS;

// Create instance for automatic registration
AccountProtectionInit account_protection_init_stage;

//--- Configure custom parameters if specified
void AccountProtectionInit::ConfigureCustomParameters()
{
    if (m_accountProtection == NULL) return;
    
    // Set custom parameters if specified (non-zero values override defaults)
        m_accountProtection.SetMaxLossPercent(MaxLossPercent);
        m_accountProtection.SetMaxDailyLoss(MaxDailyLoss);
        m_accountProtection.SetMaxDrawdownPercent(MaxDrawdownPercent);
        m_accountProtection.SetMaxOpenOrders(MaxOpenOrders);
        m_accountProtection.SetMaxLotSize(MaxLotSize);
        m_accountProtection.SetMaxTotalLotSize(MaxTotalLotSize);
        m_accountProtection.SetMaxSpread(MaxSpread);
    //  Set configuration
        m_accountProtection.SetEnabled(EnableAccountProtection);
}

//--- Initialize account protection system
bool AccountProtectionInit::InitializeAccountProtection()
{
    Print("Initializing account protection system...");
    
    // Check account protection instance
    if (m_accountProtection == NULL)
    {
        Print("ERROR: Failed to create account protection instance");
        return false;
    }

    // Check if account protection is enabled
    if (!m_accountProtection.IsEnabled())
    {
        Print("Account protection is disabled by user settings");
        return false;
    }
    
    // Validate custom parameters
    if (!m_accountProtection.Validate())
    {
        Print("ERROR: Invalid account protection parameters: ", 
                m_accountProtection.GetLastErrorMessage());
        return false;
    }

    // Initialize the account protection system
    if (!m_accountProtection.Initialize())
    {
        Print("ERROR: Account protection initialization failed: ", 
                m_accountProtection.GetLastErrorMessage());
        delete m_accountProtection;
        m_accountProtection = NULL;
        return false;
    }
    
    Print("Account protection system initialized with level: ", 
            EnumToString(m_accountProtection.GetProtectionLevel()));
    return true;
}

//--- Register account protection instance with framework
bool AccountProtectionInit::RegisterAccountProtection()
{
    if (!m_accountProtection.IsEnabled() || m_accountProtection == NULL)
    {
        Print("Account protection registration skipped (disabled or not initialized)");
        return true;
    }
    
    Print("Registering account protection with EA_Wizard framework...");
    
    // Register with the framework's object registry
    if (!Register(m_accountProtection, AccProtectionName, AccProtectionDesc))
    {
        Print("ERROR: Failed to register account protection with framework");
        return false;
    }
    
    Print("Account protection registered successfully");
    return true;
}

//--- Log successful initialization
void AccountProtectionInit::LogInitializationSuccess()
{
    if (!LogProtectionEvents) return;
    
    Print("=== Account Protection Initialization Summary ===");
    Print("Protection Level: ", EnumToString(m_accountProtection.GetProtectionLevel()));
    Print("Protection Enabled: ", m_accountProtection.IsEnabled() ? "YES" : "NO");
    
    if (m_accountProtection.IsEnabled() && m_accountProtection != NULL)
    {
        Print("Max Loss Percent: ", DoubleToString(m_accountProtection.GetMaxLossPercent(), 2), "%");
        Print("Current Status: ", m_accountProtection.GetStatusDescription());
        Print("Trading Allowed: ", m_accountProtection.IsTradingAllowed() ? "YES" : "NO");
    }

    Print("=== Initialization Complete ===");
}

#endif // ACCOUNT_PROTECTION_INIT_MQH
