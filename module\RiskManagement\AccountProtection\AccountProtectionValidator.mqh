//+------------------------------------------------------------------+
//|                                    AccountProtectionValidator.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef ACCOUNT_PROTECTION_VALIDATOR_MQH
#define ACCOUNT_PROTECTION_VALIDATOR_MQH

#include "../../Utils/Validation/ParameterValidator.mqh"

//+------------------------------------------------------------------+
//| AccountProtectionValidator Class                                 |
//| Specialized validator for account protection parameters         |
//| Inherits from ParameterValidator base class                     |
//+------------------------------------------------------------------+
class AccountProtectionValidator : public ParameterValidator
{
private:
    // Validation constants
    static const double MIN_LOSS_PERCENT;
    static const double MAX_LOSS_PERCENT;
    static const double MIN_DRAWDOWN_PERCENT;
    static const double MAX_DRAWDOWN_PERCENT;
    static const int    MIN_ORDERS;
    static const int    MAX_ORDERS;
    static const double MIN_LOT_SIZE;
    static const double MAX_LOT_SIZE;
    static const double MIN_SPREAD;
    static const double MAX_SPREAD;

public:
    //--- Constructor and Destructor
                        AccountProtectionValidator(bool strictMode = true);
    virtual            ~AccountProtectionValidator();
    
    //--- Account protection specific validation methods
    ValidationResult    ValidateMaxLossPercent(double lossPercent);
    ValidationResult    ValidateMaxDrawdownPercent(double drawdownPercent);
    ValidationResult    ValidateMaxOrders(int maxOrders);
    ValidationResult    ValidateMaxLotSize(double lotSize);
    ValidationResult    ValidateMaxTotalLotSize(double totalLotSize);
    ValidationResult    ValidateMaxSpread(double spread);
    ValidationResult    ValidateProtectionLevel(int protectionLevel);
    ValidationResult    ValidateMinEquityPercent(double equityPercent);
    ValidationResult    ValidateMaxDailyLoss(double dailyLoss, double accountBalance);
    
    //--- Comprehensive validation methods
    bool                ValidateAllProtectionParameters(
                            double maxLossPercent,
                            double maxDrawdownPercent,
                            int maxOrders,
                            double maxLotSize,
                            double maxTotalLotSize,
                            double maxSpread,
                            int protectionLevel
                        );
    
    //--- Override base class methods
    virtual bool        OnInitialize() override;
    virtual bool        OnValidate() override;
    
    //--- Utility methods
    string              GetProtectionLevelName(int level);
    bool                IsValidProtectionLevel(int level);
};

//+------------------------------------------------------------------+
//| Static constants definition                                      |
//+------------------------------------------------------------------+
static const double AccountProtectionValidator::MIN_LOSS_PERCENT = 1.0;
static const double AccountProtectionValidator::MAX_LOSS_PERCENT = 50.0;
static const double AccountProtectionValidator::MIN_DRAWDOWN_PERCENT = 1.0;
static const double AccountProtectionValidator::MAX_DRAWDOWN_PERCENT = 80.0;
static const int    AccountProtectionValidator::MIN_ORDERS = 1;
static const int    AccountProtectionValidator::MAX_ORDERS = 100;
static const double AccountProtectionValidator::MIN_LOT_SIZE = 0.01;
static const double AccountProtectionValidator::MAX_LOT_SIZE = 10.0;
static const double AccountProtectionValidator::MIN_SPREAD = 0.0;
static const double AccountProtectionValidator::MAX_SPREAD = 20.0;

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
AccountProtectionValidator::AccountProtectionValidator(bool strictMode = true) 
    : ParameterValidator(strictMode)
{
    // Set component name for logging
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
AccountProtectionValidator::~AccountProtectionValidator()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize validator                                             |
//+------------------------------------------------------------------+
bool AccountProtectionValidator::OnInitialize()
{
    // Call parent initialization
    if (!ParameterValidator::OnInitialize())
        return false;
    
    Print("AccountProtectionValidator: Specialized validator initialized");
    return true;
}

//+------------------------------------------------------------------+
//| Validate validator parameters                                    |
//+------------------------------------------------------------------+
bool AccountProtectionValidator::OnValidate()
{
    // Call parent validation
    return ParameterValidator::OnValidate();
}

//+------------------------------------------------------------------+
//| Validate maximum loss percentage                                 |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxLossPercent(double lossPercent)
{
    bool isValid = (lossPercent >= MIN_LOSS_PERCENT && lossPercent <= MAX_LOSS_PERCENT);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Maximum loss percentage " + DoubleToString(lossPercent, 2) + "% is outside valid range";
        expectedRange = DoubleToString(MIN_LOSS_PERCENT, 1) + "% - " + DoubleToString(MAX_LOSS_PERCENT, 1) + "%";
    }
    
    ValidationResult result = CreateResult(isValid, "MaxLossPercent", errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate maximum drawdown percentage                             |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxDrawdownPercent(double drawdownPercent)
{
    bool isValid = (drawdownPercent >= MIN_DRAWDOWN_PERCENT && drawdownPercent <= MAX_DRAWDOWN_PERCENT);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Maximum drawdown percentage " + DoubleToString(drawdownPercent, 2) + "% is outside valid range";
        expectedRange = DoubleToString(MIN_DRAWDOWN_PERCENT, 1) + "% - " + DoubleToString(MAX_DRAWDOWN_PERCENT, 1) + "%";
    }
    
    ValidationResult result = CreateResult(isValid, "MaxDrawdownPercent", errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate maximum orders count                                    |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxOrders(int maxOrders)
{
    bool isValid = (maxOrders >= MIN_ORDERS && maxOrders <= MAX_ORDERS);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Maximum orders count " + IntegerToString(maxOrders) + " is outside valid range";
        expectedRange = IntegerToString(MIN_ORDERS) + " - " + IntegerToString(MAX_ORDERS);
    }
    
    ValidationResult result = CreateResult(isValid, "MaxOrders", errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate maximum lot size                                        |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxLotSize(double lotSize)
{
    bool isValid = (lotSize >= MIN_LOT_SIZE && lotSize <= MAX_LOT_SIZE);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Maximum lot size " + DoubleToString(lotSize, 2) + " is outside valid range";
        expectedRange = DoubleToString(MIN_LOT_SIZE, 2) + " - " + DoubleToString(MAX_LOT_SIZE, 1);
    }
    
    ValidationResult result = CreateResult(isValid, "MaxLotSize", errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate maximum total lot size                                  |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxTotalLotSize(double totalLotSize)
{
    bool isValid = (totalLotSize >= MIN_LOT_SIZE && totalLotSize <= MAX_LOT_SIZE * 10.0);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Maximum total lot size " + DoubleToString(totalLotSize, 2) + " is outside valid range";
        expectedRange = DoubleToString(MIN_LOT_SIZE, 2) + " - " + DoubleToString(MAX_LOT_SIZE * 10.0, 1);
    }
    
    ValidationResult result = CreateResult(isValid, "MaxTotalLotSize", errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate maximum spread                                          |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxSpread(double spread)
{
    bool isValid = (spread >= MIN_SPREAD && spread <= MAX_SPREAD);
    string errorMsg = "";
    string expectedRange = "";
    
    if (!isValid)
    {
        errorMsg = "Maximum spread " + DoubleToString(spread, 1) + " points is outside valid range";
        expectedRange = DoubleToString(MIN_SPREAD, 1) + " - " + DoubleToString(MAX_SPREAD, 1) + " points";
    }
    
    ValidationResult result = CreateResult(isValid, "MaxSpread", errorMsg, expectedRange);
    LogValidationResult(result);
    
    return result;
}

//+------------------------------------------------------------------+
//| Validate protection level                                        |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateProtectionLevel(int protectionLevel)
{
    bool isValid = IsValidProtectionLevel(protectionLevel);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Invalid protection level " + IntegerToString(protectionLevel);
        expectedRange = "0-3 (NONE, BASIC, MODERATE, STRICT)";
    }

    ValidationResult result = CreateResult(isValid, "ProtectionLevel", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate minimum equity percentage                               |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMinEquityPercent(double equityPercent)
{
    bool isValid = (equityPercent >= 50.0 && equityPercent <= 99.0);
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Minimum equity percentage " + DoubleToString(equityPercent, 2) + "% is outside valid range";
        expectedRange = "50.0% - 99.0%";
    }

    ValidationResult result = CreateResult(isValid, "MinEquityPercent", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate maximum daily loss                                      |
//+------------------------------------------------------------------+
ValidationResult AccountProtectionValidator::ValidateMaxDailyLoss(double dailyLoss, double accountBalance)
{
    bool isValid = (dailyLoss >= 0.0 && dailyLoss <= accountBalance * 0.25); // Max 25% of balance per day
    string errorMsg = "";
    string expectedRange = "";

    if (!isValid)
    {
        errorMsg = "Maximum daily loss " + DoubleToString(dailyLoss, 2) + " is outside valid range";
        expectedRange = "0.0 - " + DoubleToString(accountBalance * 0.25, 2) + " (25% of balance)";
    }

    ValidationResult result = CreateResult(isValid, "MaxDailyLoss", errorMsg, expectedRange);
    LogValidationResult(result);

    return result;
}

//+------------------------------------------------------------------+
//| Validate all protection parameters                               |
//+------------------------------------------------------------------+
bool AccountProtectionValidator::ValidateAllProtectionParameters(
    double maxLossPercent,
    double maxDrawdownPercent,
    int maxOrders,
    double maxLotSize,
    double maxTotalLotSize,
    double maxSpread,
    int protectionLevel)
{
    bool allValid = true;

    // Validate each parameter
    ValidationResult result1 = ValidateMaxLossPercent(maxLossPercent);
    ValidationResult result2 = ValidateMaxDrawdownPercent(maxDrawdownPercent);
    ValidationResult result3 = ValidateMaxOrders(maxOrders);
    ValidationResult result4 = ValidateMaxLotSize(maxLotSize);
    ValidationResult result5 = ValidateMaxTotalLotSize(maxTotalLotSize);
    ValidationResult result6 = ValidateMaxSpread(maxSpread);
    ValidationResult result7 = ValidateProtectionLevel(protectionLevel);

    allValid = result1.isValid && result2.isValid && result3.isValid &&
               result4.isValid && result5.isValid && result6.isValid && result7.isValid;

    // Additional cross-validation
    if (allValid)
    {
        // Check if total lot size is reasonable compared to max lot size
        if (maxTotalLotSize < maxLotSize)
        {
            Print("AccountProtectionValidator: Warning - Total lot size is less than max lot size");
        }

        // Check if loss and drawdown percentages are consistent
        if (maxLossPercent > maxDrawdownPercent)
        {
            Print("AccountProtectionValidator: Warning - Max loss exceeds max drawdown");
        }
    }

    return allValid;
}

//+------------------------------------------------------------------+
//| Get protection level name                                        |
//+------------------------------------------------------------------+
string AccountProtectionValidator::GetProtectionLevelName(int level)
{
    switch(level)
    {
        case 0: return "NONE";
        case 1: return "BASIC";
        case 2: return "MODERATE";
        case 3: return "STRICT";
        default: return "UNKNOWN";
    }
}

//+------------------------------------------------------------------+
//| Check if protection level is valid                               |
//+------------------------------------------------------------------+
bool AccountProtectionValidator::IsValidProtectionLevel(int level)
{
    return (level >= 0 && level <= 3);
}

#endif // ACCOUNT_PROTECTION_VALIDATOR_MQH
