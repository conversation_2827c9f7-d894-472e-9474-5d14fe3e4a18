//+------------------------------------------------------------------+
//|                                      TradingPipelineContainer.mqh |
//|                                            PipelineAdvance_v1     |
//|                                                                  |
//+------------------------------------------------------------------+
#property strict

#include "TradingPipelineContainerBase.mqh"

//+------------------------------------------------------------------+
//| 統一的交易流水線容器類                                           |
//| 繼承自 TradingPipelineContainerBase 抽象基類                    |
//| 實現具體的執行邏輯                                               |
//+------------------------------------------------------------------+
class TradingPipelineContainer : public TradingPipelineContainerBase
{
public:
    // 構造函數
    TradingPipelineContainer(string name,
                           string description = "",
                           string type = "TradingPipelineContainer",
                           bool owned = false,
                           int maxPipelines = 50)
        : TradingPipelineContainerBase(name, description, type, owned, maxPipelines)
    {
    }

    // 析構函數
    virtual ~TradingPipelineContainer() {}

protected:
    // 實現抽象方法 - 執行具體邏輯
    virtual void ExecuteInternal() override
    {
        bool allSuccess = true;
        int executedCount = 0;

        foreachv(ITradingPipeline*, pipeline, GetPointer(m_pipelines))
        {
            if(pipeline != NULL)
            {
                pipeline.Execute();
                executedCount++;

                // 如果子流水線執行失敗，記錄但繼續執行其他流水線
                if(!pipeline.IsExecuted())
                {
                    allSuccess = false;
                }
            }
        }

        SetResult(allSuccess,
            StringFormat("執行完成，共執行 %d 個流水線，%s",
                executedCount,
                allSuccess ? "全部成功" : "部分失敗"),
            allSuccess ? ERROR_LEVEL_INFO : ERROR_LEVEL_WARNING);
    }
};

//+------------------------------------------------------------------+
//| 事件流水線容器類                                                 |
//| 繼承自 TradingPipelineContainer，增加事件類型支持               |
//+------------------------------------------------------------------+
class EventPipeline : public TradingPipelineContainer
{
private:
    ENUM_TRADING_EVENT m_event;            // 交易事件

public:
    // 構造函數
    EventPipeline(string name,
                 ENUM_TRADING_EVENT event,
                 string description = "",
                 string type = "EventPipeline",
                 bool owned = false,
                 int maxPipelines = 50)
        : TradingPipelineContainer(name, description, type, owned, maxPipelines),
          m_event(event)
    {
    }

    // 析構函數
    virtual ~EventPipeline() {}

    // 獲取交易事件
    ENUM_TRADING_EVENT GetEvent() const
    {
        return m_event;
    }
};

//+------------------------------------------------------------------+
//| 階段流水線容器類                                                 |
//| 繼承自 TradingPipelineContainer，增加階段類型支持               |
//+------------------------------------------------------------------+
class StagePipeline : public TradingPipelineContainer
{
private:
    ENUM_TRADING_STAGE m_stage;            // 交易階段

public:
    // 構造函數
    StagePipeline(string name,
                 ENUM_TRADING_STAGE stage,
                 string description = "",
                 string type = "StagePipeline",
                 bool owned = false,
                 int maxPipelines = 50)
        : TradingPipelineContainer(name, description, type, owned, maxPipelines),
          m_stage(stage)
    {
    }

    // 析構函數
    virtual ~StagePipeline() {}

    // 獲取交易階段
    ENUM_TRADING_STAGE GetStage() const
    {
        return m_stage;
    }
};
