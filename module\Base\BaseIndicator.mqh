//+------------------------------------------------------------------+
//|                                                BaseIndicator.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef BASE_INDICATOR_MQH
#define BASE_INDICATOR_MQH

#include "BaseComponent.mqh"

//+------------------------------------------------------------------+
//| Signal Types Enumeration                                         |
//+------------------------------------------------------------------+
enum ENUM_SIGNA<PERSON>_TYPE
{
    SIGNAL_NONE = 0,        // No signal
    SIGNAL_BUY = 1,         // Buy signal
    SIGNAL_SELL = -1,       // Sell signal
    SIGNAL_NEUTRAL = 2      // Neutral signal
};

//+------------------------------------------------------------------+
//| Signal Strength Enumeration                                      |
//+------------------------------------------------------------------+
enum ENUM_SIGNAL_STRENGTH
{
    STRENGTH_WEAK = 1,      // Weak signal
    STRENGTH_MEDIUM = 2,    // Medium signal
    STRENGTH_STRONG = 3     // Strong signal
};

//+------------------------------------------------------------------+
//| Signal Information Structure                                     |
//+------------------------------------------------------------------+
struct SignalInfo
{
    ENUM_SIGNAL_TYPE      type;           // Signal type
    ENUM_SIGNAL_STRENGTH  strength;       // Signal strength
    double                confidence;     // Confidence level (0.0 - 1.0)
    datetime              timestamp;      // Signal timestamp
    double                value;          // Indicator value
    string                description;    // Signal description
};

//+------------------------------------------------------------------+
//| BaseIndicator Class                                              |
//| Base interface for all technical indicator implementations      |
//+------------------------------------------------------------------+
class BaseIndicator : public BaseComponent
{
private:
    string            m_symbol;           // Trading symbol
    ENUM_TIMEFRAMES   m_timeframe;        // Chart timeframe
    int               m_period;           // Indicator period
    SignalInfo        m_lastSignal;       // Last generated signal
    double            m_buffer[];         // Indicator value buffer
    int               m_bufferSize;       // Buffer size

protected:
    //--- Protected methods for derived classes
    void              SetSignal(ENUM_SIGNAL_TYPE type, ENUM_SIGNAL_STRENGTH strength, 
                                double confidence, double value, string description = "");
    bool              ResizeBuffer(int size);
    void              SetBufferValue(int index, double value);
    double            GetBufferValue(int index) const;

public:
    //--- Constructor and Destructor
                      BaseIndicator(string name, string symbol = "", 
                                   ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT, 
                                   int period = 14);
    virtual          ~BaseIndicator();
    
    //--- Configuration methods
    void              SetSymbol(string symbol) { m_symbol = symbol; }
    void              SetTimeframe(ENUM_TIMEFRAMES timeframe) { m_timeframe = timeframe; }
    void              SetPeriod(int period) { m_period = period; }
    
    //--- Information methods
    string            GetSymbol() const { return m_symbol; }
    ENUM_TIMEFRAMES   GetTimeframe() const { return m_timeframe; }
    int               GetPeriod() const { return m_period; }
    
    //--- Signal methods
    SignalInfo        GetLastSignal() const { return m_lastSignal; }
    ENUM_SIGNAL_TYPE  GetSignalType() const { return m_lastSignal.type; }
    ENUM_SIGNAL_STRENGTH GetSignalStrength() const { return m_lastSignal.strength; }
    double            GetSignalConfidence() const { return m_lastSignal.confidence; }
    
    //--- Calculation methods
    virtual double    Calculate(int shift = 0) = 0;
    virtual bool      CalculateBuffer(int start = 0, int count = 1);
    virtual SignalInfo GenerateSignal(int shift = 0) = 0;
    
    //--- Validation methods
    virtual bool      IsValidShift(int shift) const;
    virtual bool      IsDataAvailable(int shift = 0) const;
    
    //--- Override base class methods
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    virtual bool      OnUpdate() override;
    
    //--- Utility methods
    double            GetPrice(int priceType, int shift = 0) const;
    datetime          GetTime(int shift = 0) const;
    int               GetBarsCount() const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
BaseIndicator::BaseIndicator(string name, string symbol = "", 
                            ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT, 
                            int period = 14) : BaseComponent(name)
{
    m_symbol = (symbol == "") ? Symbol() : symbol;
    m_timeframe = (timeframe == PERIOD_CURRENT) ? Period() : timeframe;
    m_period = period;
    m_bufferSize = 0;
    
    // Initialize signal structure
    m_lastSignal.type = SIGNAL_NONE;
    m_lastSignal.strength = STRENGTH_WEAK;
    m_lastSignal.confidence = 0.0;
    m_lastSignal.timestamp = 0;
    m_lastSignal.value = 0.0;
    m_lastSignal.description = "";
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
BaseIndicator::~BaseIndicator()
{
    ArrayFree(m_buffer);
}

//+------------------------------------------------------------------+
//| Initialize indicator                                             |
//+------------------------------------------------------------------+
bool BaseIndicator::OnInitialize()
{
    if (m_symbol == "")
    {
        SetError(101, "Invalid symbol");
        return false;
    }
    
    if (m_period <= 0)
    {
        SetError(102, "Invalid period");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate indicator parameters                                    |
//+------------------------------------------------------------------+
bool BaseIndicator::OnValidate()
{
    if (!IsDataAvailable())
    {
        SetError(103, "Insufficient data for calculation");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update indicator                                                 |
//+------------------------------------------------------------------+
bool BaseIndicator::OnUpdate()
{
    // Calculate current value
    double currentValue = Calculate(0);
    if (currentValue == EMPTY_VALUE)
    {
        SetError(104, "Calculation failed");
        return false;
    }
    
    // Generate signal
    SignalInfo signal = GenerateSignal(0);
    if (signal.type != SIGNAL_NONE)
    {
        m_lastSignal = signal;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Set signal information                                           |
//+------------------------------------------------------------------+
void BaseIndicator::SetSignal(ENUM_SIGNAL_TYPE type, ENUM_SIGNAL_STRENGTH strength, 
                              double confidence, double value, string description = "")
{
    m_lastSignal.type = type;
    m_lastSignal.strength = strength;
    m_lastSignal.confidence = MathMax(0.0, MathMin(1.0, confidence));
    m_lastSignal.timestamp = TimeCurrent();
    m_lastSignal.value = value;
    m_lastSignal.description = description;
}

//+------------------------------------------------------------------+
//| Resize indicator buffer                                          |
//+------------------------------------------------------------------+
bool BaseIndicator::ResizeBuffer(int size)
{
    if (size <= 0)
        return false;
        
    if (ArrayResize(m_buffer, size) < 0)
        return false;
        
    m_bufferSize = size;
    return true;
}

//+------------------------------------------------------------------+
//| Set buffer value                                                 |
//+------------------------------------------------------------------+
void BaseIndicator::SetBufferValue(int index, double value)
{
    if (index >= 0 && index < m_bufferSize)
    {
        m_buffer[index] = value;
    }
}

//+------------------------------------------------------------------+
//| Get buffer value                                                 |
//+------------------------------------------------------------------+
double BaseIndicator::GetBufferValue(int index) const
{
    if (index >= 0 && index < m_bufferSize)
    {
        return m_buffer[index];
    }
    return EMPTY_VALUE;
}

//+------------------------------------------------------------------+
//| Calculate buffer values                                          |
//+------------------------------------------------------------------+
bool BaseIndicator::CalculateBuffer(int start = 0, int count = 1)
{
    for (int i = start; i < start + count; i++)
    {
        double value = Calculate(i);
        if (value == EMPTY_VALUE)
            return false;
        SetBufferValue(i, value);
    }
    return true;
}

//+------------------------------------------------------------------+
//| Check if shift is valid                                          |
//+------------------------------------------------------------------+
bool BaseIndicator::IsValidShift(int shift) const
{
    return (shift >= 0 && shift < GetBarsCount());
}

//+------------------------------------------------------------------+
//| Check if data is available                                       |
//+------------------------------------------------------------------+
bool BaseIndicator::IsDataAvailable(int shift = 0) const
{
    return (GetBarsCount() > m_period + shift);
}

//+------------------------------------------------------------------+
//| Get price value                                                  |
//+------------------------------------------------------------------+
double BaseIndicator::GetPrice(int priceType, int shift = 0) const
{
    switch(priceType)
    {
        case PRICE_OPEN:    return iOpen(m_symbol, m_timeframe, shift);
        case PRICE_HIGH:    return iHigh(m_symbol, m_timeframe, shift);
        case PRICE_LOW:     return iLow(m_symbol, m_timeframe, shift);
        case PRICE_CLOSE:   return iClose(m_symbol, m_timeframe, shift);
        case PRICE_MEDIAN:  return (iHigh(m_symbol, m_timeframe, shift) + iLow(m_symbol, m_timeframe, shift)) / 2.0;
        case PRICE_TYPICAL: return (iHigh(m_symbol, m_timeframe, shift) + iLow(m_symbol, m_timeframe, shift) + iClose(m_symbol, m_timeframe, shift)) / 3.0;
        case PRICE_WEIGHTED: return (iHigh(m_symbol, m_timeframe, shift) + iLow(m_symbol, m_timeframe, shift) + 2 * iClose(m_symbol, m_timeframe, shift)) / 4.0;
        default:            return iClose(m_symbol, m_timeframe, shift);
    }
}

//+------------------------------------------------------------------+
//| Get time value                                                   |
//+------------------------------------------------------------------+
datetime BaseIndicator::GetTime(int shift = 0) const
{
    return iTime(m_symbol, m_timeframe, shift);
}

//+------------------------------------------------------------------+
//| Get bars count                                                   |
//+------------------------------------------------------------------+
int BaseIndicator::GetBarsCount() const
{
    return iBars(m_symbol, m_timeframe);
}

#endif // BASE_INDICATOR_MQH
