//+------------------------------------------------------------------+
//|                                          TradingController.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef TRADING_CONTROLLER_MQH
#define TRADING_CONTROLLER_MQH

#include "../../Base/BaseComponent.mqh"
#include "Enum.mqh"
#include "RiskChecker.mqh"
#include "ProtectionStatusManager.mqh"

//+------------------------------------------------------------------+
//| TradingController Class                                          |
//| Responsible for controlling trading permissions and actions     |
//| Single Responsibility: Trading Control                          |
//+------------------------------------------------------------------+
class TradingController : public BaseComponent
{
private:
    RiskChecker*            m_riskChecker;          // Risk checker component
    ProtectionStatusManager* m_statusManager;       // Status manager component
    
    bool                    m_tradingHalted;        // Trading halt flag
    bool                    m_emergencyStop;        // Emergency stop flag
    string                  m_haltReason;           // Reason for trading halt
    datetime                m_haltTime;             // Time when trading was halted
    
    // Control flags
    bool                    m_allowNewOrders;       // Allow new orders flag
    bool                    m_allowOrderModification; // Allow order modification flag
    bool                    m_allowOrderClosure;    // Allow order closure flag
    
public:
    //--- Constructor and Destructor
                            TradingController(RiskChecker* riskChecker, 
                                            ProtectionStatusManager* statusManager);
    virtual                ~TradingController();
    
    //--- Trading control methods
    bool                    IsTradingAllowed();
    bool                    IsNewOrderAllowed(double lotSize = 0.0, string symbol = "");
    bool                    IsOrderModificationAllowed();
    bool                    IsOrderClosureAllowed();
    
    //--- Trading halt methods
    void                    HaltTrading(string reason = "");
    void                    ResumeTrading();
    void                    EmergencyStop(string reason = "");
    void                    ClearEmergencyStop();
    
    //--- Information methods
    bool                    IsTradingHalted() const { return m_tradingHalted; }
    bool                    IsEmergencyStop() const { return m_emergencyStop; }
    string                  GetHaltReason() const { return m_haltReason; }
    datetime                GetHaltTime() const { return m_haltTime; }
    
    //--- Control flag methods
    void                    SetAllowNewOrders(bool allow) { m_allowNewOrders = allow; }
    void                    SetAllowOrderModification(bool allow) { m_allowOrderModification = allow; }
    void                    SetAllowOrderClosure(bool allow) { m_allowOrderClosure = allow; }
    bool                    IsNewOrdersAllowed() const { return m_allowNewOrders; }
    bool                    IsOrderModificationAllowed() const { return m_allowOrderModification; }
    bool                    IsOrderClosureAllowed() const { return m_allowOrderClosure; }
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    string                  GetControlSummary() const;
    void                    LogTradingStatus() const;
    
private:
    //--- Internal methods
    bool                    CheckBasicTradingConditions();
    bool                    CheckRiskConditions(double lotSize = 0.0, string symbol = "");
    bool                    CheckStatusConditions();
    void                    UpdateControlFlags();
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
TradingController::TradingController(RiskChecker* riskChecker, 
                                   ProtectionStatusManager* statusManager) : BaseComponent("TradingController")
{
    m_riskChecker = riskChecker;
    m_statusManager = statusManager;
    
    m_tradingHalted = false;
    m_emergencyStop = false;
    m_haltReason = "";
    m_haltTime = 0;
    
    m_allowNewOrders = true;
    m_allowOrderModification = true;
    m_allowOrderClosure = true;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
TradingController::~TradingController()
{
    // Components are managed externally
}

//+------------------------------------------------------------------+
//| Initialize trading controller                                    |
//+------------------------------------------------------------------+
bool TradingController::OnInitialize()
{
    if (m_riskChecker == NULL)
    {
        SetError(6001, "Risk checker component not set");
        return false;
    }
    
    if (m_statusManager == NULL)
    {
        SetError(6002, "Status manager component not set");
        return false;
    }
    
    if (!m_riskChecker.IsInitialized() || !m_statusManager.IsInitialized())
    {
        SetError(6003, "One or more components not initialized");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate trading controller                                      |
//+------------------------------------------------------------------+
bool TradingController::OnValidate()
{
    if (m_riskChecker == NULL || m_statusManager == NULL)
    {
        SetError(6004, "Component validation failed");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update trading controller                                        |
//+------------------------------------------------------------------+
bool TradingController::OnUpdate()
{
    UpdateControlFlags();
    
    // Auto-halt trading if emergency status
    if (m_statusManager != NULL && m_statusManager.IsEmergency() && !m_emergencyStop)
    {
        EmergencyStop("Emergency status detected");
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool TradingController::IsTradingAllowed()
{
    if (m_tradingHalted || m_emergencyStop)
        return false;
    
    return CheckBasicTradingConditions() && 
           CheckRiskConditions() && 
           CheckStatusConditions();
}

//+------------------------------------------------------------------+
//| Check if new order is allowed                                    |
//+------------------------------------------------------------------+
bool TradingController::IsNewOrderAllowed(double lotSize = 0.0, string symbol = "")
{
    if (!m_allowNewOrders)
        return false;
    
    if (!IsTradingAllowed())
        return false;
    
    return CheckRiskConditions(lotSize, symbol);
}

//+------------------------------------------------------------------+
//| Check if order modification is allowed                           |
//+------------------------------------------------------------------+
bool TradingController::IsOrderModificationAllowed()
{
    if (!m_allowOrderModification)
        return false;
    
    if (m_emergencyStop)
        return false;
    
    return CheckBasicTradingConditions();
}

//+------------------------------------------------------------------+
//| Check if order closure is allowed                                |
//+------------------------------------------------------------------+
bool TradingController::IsOrderClosureAllowed()
{
    if (!m_allowOrderClosure)
        return false;
    
    // Order closure should always be allowed unless explicitly disabled
    return CheckBasicTradingConditions();
}

//+------------------------------------------------------------------+
//| Halt trading                                                     |
//+------------------------------------------------------------------+
void TradingController::HaltTrading(string reason = "")
{
    if (!m_tradingHalted)
    {
        m_tradingHalted = true;
        m_haltReason = (reason != "") ? reason : "Trading halted";
        m_haltTime = TimeCurrent();
        
        Print("TRADING HALTED: ", m_haltReason);
        Print("  Time: ", TimeToString(m_haltTime));
    }
}

//+------------------------------------------------------------------+
//| Resume trading                                                   |
//+------------------------------------------------------------------+
void TradingController::ResumeTrading()
{
    if (m_tradingHalted && !m_emergencyStop)
    {
        m_tradingHalted = false;
        m_haltReason = "";
        m_haltTime = 0;
        
        Print("TRADING RESUMED");
    }
}

//+------------------------------------------------------------------+
//| Emergency stop                                                   |
//+------------------------------------------------------------------+
void TradingController::EmergencyStop(string reason = "")
{
    m_emergencyStop = true;
    m_tradingHalted = true;
    m_haltReason = "EMERGENCY: " + ((reason != "") ? reason : "Emergency stop activated");
    m_haltTime = TimeCurrent();
    
    // Disable all trading activities
    m_allowNewOrders = false;
    m_allowOrderModification = false;
    // Keep order closure allowed for emergency exit
    
    Print("EMERGENCY STOP ACTIVATED: ", m_haltReason);
    Print("  Time: ", TimeToString(m_haltTime));
}

//+------------------------------------------------------------------+
//| Clear emergency stop                                             |
//+------------------------------------------------------------------+
void TradingController::ClearEmergencyStop()
{
    if (m_emergencyStop)
    {
        m_emergencyStop = false;
        m_tradingHalted = false;
        m_haltReason = "";
        m_haltTime = 0;
        
        // Restore default permissions
        m_allowNewOrders = true;
        m_allowOrderModification = true;
        m_allowOrderClosure = true;
        
        Print("EMERGENCY STOP CLEARED - Trading permissions restored");
    }
}

//+------------------------------------------------------------------+
//| Get control summary                                              |
//+------------------------------------------------------------------+
string TradingController::GetControlSummary() const
{
    string summary = "Trading Control Status:\n";
    summary += "  Trading Halted: " + (m_tradingHalted ? "YES" : "NO") + "\n";
    summary += "  Emergency Stop: " + (m_emergencyStop ? "YES" : "NO") + "\n";
    summary += "  New Orders: " + (m_allowNewOrders ? "ALLOWED" : "BLOCKED") + "\n";
    summary += "  Modifications: " + (m_allowOrderModification ? "ALLOWED" : "BLOCKED") + "\n";
    summary += "  Closures: " + (m_allowOrderClosure ? "ALLOWED" : "BLOCKED") + "\n";
    
    if (m_haltReason != "")
        summary += "  Halt Reason: " + m_haltReason + "\n";
    
    if (m_haltTime > 0)
        summary += "  Halt Time: " + TimeToString(m_haltTime);
    
    return summary;
}

//+------------------------------------------------------------------+
//| Log trading status                                               |
//+------------------------------------------------------------------+
void TradingController::LogTradingStatus() const
{
    Print("TradingController Status:");
    Print("  Halted: ", (m_tradingHalted ? "YES" : "NO"));
    Print("  Emergency: ", (m_emergencyStop ? "YES" : "NO"));
    Print("  New Orders: ", (m_allowNewOrders ? "ALLOWED" : "BLOCKED"));
    Print("  Modifications: ", (m_allowOrderModification ? "ALLOWED" : "BLOCKED"));
    Print("  Closures: ", (m_allowOrderClosure ? "ALLOWED" : "BLOCKED"));
    
    if (m_haltReason != "")
        Print("  Reason: ", m_haltReason);
}

//+------------------------------------------------------------------+
//| Check basic trading conditions                                   |
//+------------------------------------------------------------------+
bool TradingController::CheckBasicTradingConditions()
{
    // Check if trading is allowed by terminal
    if (!IsTradeAllowed())
        return false;
    
    // Check if market is open (basic check)
    if (MarketInfo(Symbol(), MODE_TRADEALLOWED) <= 0)
        return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Check risk conditions                                            |
//+------------------------------------------------------------------+
bool TradingController::CheckRiskConditions(double lotSize = 0.0, string symbol = "")
{
    if (m_riskChecker == NULL)
        return false;
    
    return m_riskChecker.CheckAllRisks(lotSize, symbol);
}

//+------------------------------------------------------------------+
//| Check status conditions                                          |
//+------------------------------------------------------------------+
bool TradingController::CheckStatusConditions()
{
    if (m_statusManager == NULL)
        return true; // If no status manager, assume OK
    
    // Don't allow trading in emergency status
    return !m_statusManager.IsEmergency();
}

//+------------------------------------------------------------------+
//| Update control flags based on current conditions                |
//+------------------------------------------------------------------+
void TradingController::UpdateControlFlags()
{
    if (m_emergencyStop)
    {
        m_allowNewOrders = false;
        m_allowOrderModification = false;
        // Keep closure allowed for emergency exit
        return;
    }
    
    if (m_statusManager != NULL)
    {
        if (m_statusManager.IsEmergency())
        {
            m_allowNewOrders = false;
            m_allowOrderModification = false;
        }
        else if (m_statusManager.IsCritical())
        {
            m_allowNewOrders = false;
            m_allowOrderModification = true;
        }
        else
        {
            m_allowNewOrders = true;
            m_allowOrderModification = true;
        }
    }
    
    // Order closure should always be allowed unless explicitly disabled
    if (!m_emergencyStop)
        m_allowOrderClosure = true;
}

#endif // TRADING_CONTROLLER_MQH
