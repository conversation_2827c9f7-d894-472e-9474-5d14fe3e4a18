//+------------------------------------------------------------------+
//|                                                          RSI.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef RSI_MQH
#define RSI_MQH

#include "../../Base/BaseIndicator.mqh"

//+------------------------------------------------------------------+
//| RSI Class                                                        |
//| Implementation of RSI (Relative Strength Index) indicator       |
//+------------------------------------------------------------------+
class RSI : public BaseIndicator
{
private:
    int               m_period;           // RSI period
    int               m_appliedPrice;     // Applied price type
    double            m_overboughtLevel;  // Overbought threshold
    double            m_oversoldLevel;    // Oversold threshold
    
    // RSI values
    double            m_rsiValue;         // Current RSI value

public:
    //--- Constructor and Destructor
                      RSI(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
                         int period = 14, int appliedPrice = PRICE_CLOSE,
                         double overboughtLevel = 70.0, double oversoldLevel = 30.0);
    virtual          ~RSI();
    
    //--- Configuration methods
    void              SetRSIPeriod(int period) { m_period = MathMax(1, period); }
    void              SetAppliedPrice(int price) { m_appliedPrice = price; }
    void              SetOverboughtLevel(double level) { m_overboughtLevel = MathMax(50.0, MathMin(100.0, level)); }
    void              SetOversoldLevel(double level) { m_oversoldLevel = MathMax(0.0, MathMin(50.0, level)); }
    
    //--- Information methods
    int               GetRSIPeriod() const { return m_period; }
    int               GetAppliedPrice() const { return m_appliedPrice; }
    double            GetOverboughtLevel() const { return m_overboughtLevel; }
    double            GetOversoldLevel() const { return m_oversoldLevel; }
    
    //--- RSI values
    double            GetRSI(int shift = 0) const;
    
    //--- Override base class methods
    virtual double    Calculate(int shift = 0) override;
    virtual SignalInfo GenerateSignal(int shift = 0) override;
    virtual bool      OnInitialize() override;
    virtual bool      OnValidate() override;
    
    //--- Signal analysis methods
    bool              IsOverbought(int shift = 0) const;
    bool              IsOversold(int shift = 0) const;
    bool              IsNeutral(int shift = 0) const;
    bool              IsBullishDivergence(int shift = 0, int lookback = 10) const;
    bool              IsBearishDivergence(int shift = 0, int lookback = 10) const;
    bool              IsRising(int shift = 0, int periods = 3) const;
    bool              IsFalling(int shift = 0, int periods = 3) const;
    bool              CrossedAbove(double level, int shift = 0) const;
    bool              CrossedBelow(double level, int shift = 0) const;
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
RSI::RSI(string symbol = "", ENUM_TIMEFRAMES timeframe = PERIOD_CURRENT,
        int period = 14, int appliedPrice = PRICE_CLOSE,
        double overboughtLevel = 70.0, double oversoldLevel = 30.0)
    : BaseIndicator("RSI", symbol, timeframe, period)
{
    m_period = MathMax(1, period);
    m_appliedPrice = appliedPrice;
    m_overboughtLevel = MathMax(50.0, MathMin(100.0, overboughtLevel));
    m_oversoldLevel = MathMax(0.0, MathMin(50.0, oversoldLevel));
    
    m_rsiValue = 0.0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
RSI::~RSI()
{
    // Cleanup handled by base class
}

//+------------------------------------------------------------------+
//| Initialize RSI                                                   |
//+------------------------------------------------------------------+
bool RSI::OnInitialize()
{
    if (!BaseIndicator::OnInitialize())
        return false;
    
    if (m_period < 2)
    {
        SetError(501, "RSI period must be at least 2");
        return false;
    }
    
    if (m_overboughtLevel <= m_oversoldLevel)
    {
        SetError(502, "Overbought level must be greater than oversold level");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate RSI parameters                                          |
//+------------------------------------------------------------------+
bool RSI::OnValidate()
{
    if (!BaseIndicator::OnValidate())
        return false;
    
    if (!IsDataAvailable(m_period + 1))
    {
        SetError(503, "Insufficient data for RSI calculation");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Calculate RSI value                                              |
//+------------------------------------------------------------------+
double RSI::Calculate(int shift = 0)
{
    if (!IsValidShift(shift))
        return EMPTY_VALUE;
    
    // Use built-in RSI function
    m_rsiValue = iRSI(GetSymbol(), GetTimeframe(), m_period, m_appliedPrice, shift);
    
    return m_rsiValue;
}

//+------------------------------------------------------------------+
//| Generate trading signal                                          |
//+------------------------------------------------------------------+
SignalInfo RSI::GenerateSignal(int shift = 0)
{
    SignalInfo signal;
    signal.type = SIGNAL_NONE;
    signal.strength = STRENGTH_WEAK;
    signal.confidence = 0.0;
    signal.timestamp = GetTime(shift);
    signal.value = Calculate(shift);
    signal.description = "";
    
    if (signal.value == EMPTY_VALUE)
        return signal;
    
    // Generate signals based on RSI levels
    if (IsOversold(shift))
    {
        signal.type = SIGNAL_BUY;
        signal.strength = STRENGTH_MEDIUM;
        signal.confidence = 0.6;
        signal.description = "RSI Oversold Condition";
        
        // Check for bullish divergence
        if (IsBullishDivergence(shift))
        {
            signal.strength = STRENGTH_STRONG;
            signal.confidence = 0.8;
            signal.description = "RSI Oversold with Bullish Divergence";
        }
        
        // Check if RSI is rising from oversold
        if (IsRising(shift, 2))
        {
            signal.confidence *= 1.2;
            signal.description += " - Rising";
        }
    }
    else if (IsOverbought(shift))
    {
        signal.type = SIGNAL_SELL;
        signal.strength = STRENGTH_MEDIUM;
        signal.confidence = 0.6;
        signal.description = "RSI Overbought Condition";
        
        // Check for bearish divergence
        if (IsBearishDivergence(shift))
        {
            signal.strength = STRENGTH_STRONG;
            signal.confidence = 0.8;
            signal.description = "RSI Overbought with Bearish Divergence";
        }
        
        // Check if RSI is falling from overbought
        if (IsFalling(shift, 2))
        {
            signal.confidence *= 1.2;
            signal.description += " - Falling";
        }
    }
    else
    {
        // Check for crossover signals
        if (CrossedAbove(m_oversoldLevel, shift))
        {
            signal.type = SIGNAL_BUY;
            signal.strength = STRENGTH_WEAK;
            signal.confidence = 0.4;
            signal.description = "RSI Crossed Above Oversold Level";
        }
        else if (CrossedBelow(m_overboughtLevel, shift))
        {
            signal.type = SIGNAL_SELL;
            signal.strength = STRENGTH_WEAK;
            signal.confidence = 0.4;
            signal.description = "RSI Crossed Below Overbought Level";
        }
        else if (IsNeutral(shift))
        {
            // Neutral zone - check momentum
            if (IsRising(shift, 3))
            {
                signal.type = SIGNAL_BUY;
                signal.strength = STRENGTH_WEAK;
                signal.confidence = 0.3;
                signal.description = "RSI Rising in Neutral Zone";
            }
            else if (IsFalling(shift, 3))
            {
                signal.type = SIGNAL_SELL;
                signal.strength = STRENGTH_WEAK;
                signal.confidence = 0.3;
                signal.description = "RSI Falling in Neutral Zone";
            }
        }
    }
    
    signal.confidence = MathMax(0.0, MathMin(1.0, signal.confidence));
    
    return signal;
}

//+------------------------------------------------------------------+
//| Get RSI value                                                    |
//+------------------------------------------------------------------+
double RSI::GetRSI(int shift = 0) const
{
    return iRSI(GetSymbol(), GetTimeframe(), m_period, m_appliedPrice, shift);
}

//+------------------------------------------------------------------+
//| Check if RSI is overbought                                       |
//+------------------------------------------------------------------+
bool RSI::IsOverbought(int shift = 0) const
{
    double rsi = GetRSI(shift);
    return (rsi != EMPTY_VALUE && rsi >= m_overboughtLevel);
}

//+------------------------------------------------------------------+
//| Check if RSI is oversold                                         |
//+------------------------------------------------------------------+
bool RSI::IsOversold(int shift = 0) const
{
    double rsi = GetRSI(shift);
    return (rsi != EMPTY_VALUE && rsi <= m_oversoldLevel);
}

//+------------------------------------------------------------------+
//| Check if RSI is in neutral zone                                 |
//+------------------------------------------------------------------+
bool RSI::IsNeutral(int shift = 0) const
{
    double rsi = GetRSI(shift);
    return (rsi != EMPTY_VALUE && rsi > m_oversoldLevel && rsi < m_overboughtLevel);
}

//+------------------------------------------------------------------+
//| Check for bullish divergence                                    |
//+------------------------------------------------------------------+
bool RSI::IsBullishDivergence(int shift = 0, int lookback = 10) const
{
    if (shift + lookback >= GetBarsCount())
        return false;
    
    double currentPrice = GetPrice(m_appliedPrice, shift);
    double currentRSI = GetRSI(shift);
    double pastPrice = GetPrice(m_appliedPrice, shift + lookback);
    double pastRSI = GetRSI(shift + lookback);
    
    if (currentPrice == EMPTY_VALUE || currentRSI == EMPTY_VALUE ||
        pastPrice == EMPTY_VALUE || pastRSI == EMPTY_VALUE)
        return false;
    
    // Bullish divergence: price makes lower low, RSI makes higher low
    return (currentPrice < pastPrice && currentRSI > pastRSI && currentRSI <= m_oversoldLevel);
}

//+------------------------------------------------------------------+
//| Check for bearish divergence                                    |
//+------------------------------------------------------------------+
bool RSI::IsBearishDivergence(int shift = 0, int lookback = 10) const
{
    if (shift + lookback >= GetBarsCount())
        return false;
    
    double currentPrice = GetPrice(m_appliedPrice, shift);
    double currentRSI = GetRSI(shift);
    double pastPrice = GetPrice(m_appliedPrice, shift + lookback);
    double pastRSI = GetRSI(shift + lookback);
    
    if (currentPrice == EMPTY_VALUE || currentRSI == EMPTY_VALUE ||
        pastPrice == EMPTY_VALUE || pastRSI == EMPTY_VALUE)
        return false;
    
    // Bearish divergence: price makes higher high, RSI makes lower high
    return (currentPrice > pastPrice && currentRSI < pastRSI && currentRSI >= m_overboughtLevel);
}

//+------------------------------------------------------------------+
//| Check if RSI is rising                                          |
//+------------------------------------------------------------------+
bool RSI::IsRising(int shift = 0, int periods = 3) const
{
    if (shift + periods >= GetBarsCount())
        return false;
    
    for (int i = 0; i < periods - 1; i++)
    {
        double current = GetRSI(shift + i);
        double previous = GetRSI(shift + i + 1);
        
        if (current == EMPTY_VALUE || previous == EMPTY_VALUE || current <= previous)
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if RSI is falling                                         |
//+------------------------------------------------------------------+
bool RSI::IsFalling(int shift = 0, int periods = 3) const
{
    if (shift + periods >= GetBarsCount())
        return false;
    
    for (int i = 0; i < periods - 1; i++)
    {
        double current = GetRSI(shift + i);
        double previous = GetRSI(shift + i + 1);
        
        if (current == EMPTY_VALUE || previous == EMPTY_VALUE || current >= previous)
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check if RSI crossed above level                                |
//+------------------------------------------------------------------+
bool RSI::CrossedAbove(double level, int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double current = GetRSI(shift);
    double previous = GetRSI(shift + 1);
    
    return (current != EMPTY_VALUE && previous != EMPTY_VALUE &&
            previous <= level && current > level);
}

//+------------------------------------------------------------------+
//| Check if RSI crossed below level                                |
//+------------------------------------------------------------------+
bool RSI::CrossedBelow(double level, int shift = 0) const
{
    if (shift + 1 >= GetBarsCount())
        return false;
    
    double current = GetRSI(shift);
    double previous = GetRSI(shift + 1);
    
    return (current != EMPTY_VALUE && previous != EMPTY_VALUE &&
            previous >= level && current < level);
}

#endif // RSI_MQH
