//+------------------------------------------------------------------+
//|                                              RiskChecker.mqh |
//|                                    EA_Wizard Framework Component |
//|                                                                  |
//+------------------------------------------------------------------+
#ifndef RISK_CHECKER_MQH
#define RISK_CHECKER_MQH

#include "../../Base/BaseComponent.mqh"
#include "AccountProtectionConfig.mqh"
#include "AccountMonitor.mqh"
#include "AccountDataProvider.mqh"

//+------------------------------------------------------------------+
//| RiskChecker Class                                                |
//| Responsible for performing various risk checks                  |
//| Single Responsibility: Risk Assessment                          |
//+------------------------------------------------------------------+
class RiskChecker : public BaseComponent
{
private:
    AccountProtectionConfig* m_config;              // Configuration component
    AccountMonitor*         m_monitor;              // Monitor component
    AccountDataProvider*    m_dataProvider;         // Data provider component
    
    // Risk check results
    bool                    m_accountLimitsOK;      // Account limits check result
    bool                    m_positionLimitsOK;     // Position limits check result
    bool                    m_dailyLimitsOK;        // Daily limits check result
    bool                    m_spreadOK;             // Spread check result
    
    string                  m_lastRiskMessage;      // Last risk check message
    
public:
    //--- Constructor and Destructor
                            RiskChecker(AccountProtectionConfig* config, 
                                      AccountMonitor* monitor, 
                                      AccountDataProvider* dataProvider);
    virtual                ~RiskChecker();
    
    //--- Risk check methods
    bool                    CheckAccountLimits();
    bool                    CheckPositionLimits(double lotSize = 0.0);
    bool                    CheckDailyLimits();
    bool                    CheckSpreadLimits(string symbol = "");
    bool                    CheckAllRisks(double lotSize = 0.0, string symbol = "");
    
    //--- Information methods
    bool                    IsAccountLimitsOK() const { return m_accountLimitsOK; }
    bool                    IsPositionLimitsOK() const { return m_positionLimitsOK; }
    bool                    IsDailyLimitsOK() const { return m_dailyLimitsOK; }
    bool                    IsSpreadOK() const { return m_spreadOK; }
    string                  GetLastRiskMessage() const { return m_lastRiskMessage; }
    
    //--- Detailed check methods
    bool                    CheckMaxLossLimit();
    bool                    CheckMaxDrawdownLimit();
    bool                    CheckMaxOrdersLimit();
    bool                    CheckMaxLotSizeLimit(double lotSize);
    bool                    CheckMaxTotalLotSizeLimit(double additionalLotSize = 0.0);
    bool                    CheckMaxSpreadLimit(string symbol = "");
    bool                    CheckMaxDailyLossLimit();
    
    //--- Override base class methods
    virtual bool            OnInitialize() override;
    virtual bool            OnValidate() override;
    virtual bool            OnUpdate() override;
    
    //--- Utility methods
    string                  GetRiskSummary() const;
    void                    LogRiskStatus() const;
    void                    SetRiskMessage(string message);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
RiskChecker::RiskChecker(AccountProtectionConfig* config, 
                        AccountMonitor* monitor, 
                        AccountDataProvider* dataProvider) : BaseComponent("RiskChecker")
{
    m_config = config;
    m_monitor = monitor;
    m_dataProvider = dataProvider;
    
    m_accountLimitsOK = true;
    m_positionLimitsOK = true;
    m_dailyLimitsOK = true;
    m_spreadOK = true;
    m_lastRiskMessage = "";
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
RiskChecker::~RiskChecker()
{
    // Components are managed externally
}

//+------------------------------------------------------------------+
//| Initialize risk checker                                          |
//+------------------------------------------------------------------+
bool RiskChecker::OnInitialize()
{
    if (m_config == NULL)
    {
        SetError(4001, "Configuration component not set");
        return false;
    }
    
    if (m_monitor == NULL)
    {
        SetError(4002, "Monitor component not set");
        return false;
    }
    
    if (m_dataProvider == NULL)
    {
        SetError(4003, "Data provider component not set");
        return false;
    }
    
    if (!m_config.IsInitialized() || !m_monitor.IsInitialized() || !m_dataProvider.IsInitialized())
    {
        SetError(4004, "One or more components not initialized");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Validate risk checker                                            |
//+------------------------------------------------------------------+
bool RiskChecker::OnValidate()
{
    if (m_config == NULL || m_monitor == NULL || m_dataProvider == NULL)
    {
        SetError(4005, "Component validation failed");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Update risk checker                                              |
//+------------------------------------------------------------------+
bool RiskChecker::OnUpdate()
{
    // Update all risk checks
    CheckAllRisks();
    return true;
}

//+------------------------------------------------------------------+
//| Check account limits                                             |
//+------------------------------------------------------------------+
bool RiskChecker::CheckAccountLimits()
{
    m_accountLimitsOK = CheckMaxLossLimit() && CheckMaxDrawdownLimit();
    
    if (!m_accountLimitsOK)
    {
        SetRiskMessage("Account limits exceeded");
    }
    
    return m_accountLimitsOK;
}

//+------------------------------------------------------------------+
//| Check position limits                                            |
//+------------------------------------------------------------------+
bool RiskChecker::CheckPositionLimits(double lotSize = 0.0)
{
    m_positionLimitsOK = CheckMaxOrdersLimit() && 
                        CheckMaxLotSizeLimit(lotSize) && 
                        CheckMaxTotalLotSizeLimit(lotSize);
    
    if (!m_positionLimitsOK)
    {
        SetRiskMessage("Position limits exceeded");
    }
    
    return m_positionLimitsOK;
}

//+------------------------------------------------------------------+
//| Check daily limits                                               |
//+------------------------------------------------------------------+
bool RiskChecker::CheckDailyLimits()
{
    m_dailyLimitsOK = CheckMaxDailyLossLimit();
    
    if (!m_dailyLimitsOK)
    {
        SetRiskMessage("Daily limits exceeded");
    }
    
    return m_dailyLimitsOK;
}

//+------------------------------------------------------------------+
//| Check spread limits                                              |
//+------------------------------------------------------------------+
bool RiskChecker::CheckSpreadLimits(string symbol = "")
{
    m_spreadOK = CheckMaxSpreadLimit(symbol);
    
    if (!m_spreadOK)
    {
        SetRiskMessage("Spread limits exceeded");
    }
    
    return m_spreadOK;
}

//+------------------------------------------------------------------+
//| Check all risks                                                  |
//+------------------------------------------------------------------+
bool RiskChecker::CheckAllRisks(double lotSize = 0.0, string symbol = "")
{
    bool allOK = CheckAccountLimits() && 
                CheckPositionLimits(lotSize) && 
                CheckDailyLimits() && 
                CheckSpreadLimits(symbol);
    
    if (allOK)
    {
        SetRiskMessage("All risk checks passed");
    }
    
    return allOK;
}

//+------------------------------------------------------------------+
//| Check maximum loss limit                                         |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxLossLimit()
{
    if (m_config == NULL || m_monitor == NULL)
        return false;
    
    double currentLossPercent = m_monitor.GetCurrentLossPercent();
    double maxLossPercent = m_config.GetMaxLossPercent();
    
    return (currentLossPercent < maxLossPercent);
}

//+------------------------------------------------------------------+
//| Check maximum drawdown limit                                     |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxDrawdownLimit()
{
    if (m_config == NULL || m_monitor == NULL)
        return false;
    
    double currentDrawdownPercent = m_monitor.GetCurrentDrawdownPercent();
    double maxDrawdownPercent = m_config.GetMaxDrawdownPercent();
    
    return (currentDrawdownPercent < maxDrawdownPercent);
}

//+------------------------------------------------------------------+
//| Check maximum orders limit                                       |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxOrdersLimit()
{
    if (m_config == NULL || m_dataProvider == NULL)
        return false;
    
    int currentOrders = m_dataProvider.GetOpenOrdersCount();
    int maxOrders = m_config.GetMaxOpenOrders();
    
    return (currentOrders < maxOrders);
}

//+------------------------------------------------------------------+
//| Check maximum lot size limit                                     |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxLotSizeLimit(double lotSize)
{
    if (m_config == NULL)
        return false;
    
    if (lotSize <= 0.0)
        return true; // No lot size to check
    
    double maxLotSize = m_config.GetMaxLotSize();
    return (lotSize <= maxLotSize);
}

//+------------------------------------------------------------------+
//| Check maximum total lot size limit                               |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxTotalLotSizeLimit(double additionalLotSize = 0.0)
{
    if (m_config == NULL || m_dataProvider == NULL)
        return false;
    
    double currentTotalLots = m_dataProvider.GetTotalLotSize();
    double maxTotalLots = m_config.GetMaxTotalLotSize();
    
    return ((currentTotalLots + additionalLotSize) <= maxTotalLots);
}

//+------------------------------------------------------------------+
//| Check maximum spread limit                                       |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxSpreadLimit(string symbol = "")
{
    if (m_config == NULL || m_dataProvider == NULL)
        return false;
    
    double currentSpread = m_dataProvider.GetCurrentSpread(symbol);
    double maxSpread = m_config.GetMaxSpread();
    
    return (currentSpread <= maxSpread);
}

//+------------------------------------------------------------------+
//| Check maximum daily loss limit                                   |
//+------------------------------------------------------------------+
bool RiskChecker::CheckMaxDailyLossLimit()
{
    if (m_config == NULL || m_monitor == NULL)
        return false;
    
    double maxDailyLoss = m_config.GetMaxDailyLoss();
    if (maxDailyLoss <= 0.0)
        return true; // No daily limit set
    
    double currentDailyLoss = m_monitor.GetDailyLoss();
    return (currentDailyLoss < maxDailyLoss);
}

//+------------------------------------------------------------------+
//| Get risk summary                                                 |
//+------------------------------------------------------------------+
string RiskChecker::GetRiskSummary() const
{
    string summary = "Risk Check Status:\n";
    summary += "  Account Limits: " + (m_accountLimitsOK ? "OK" : "FAILED") + "\n";
    summary += "  Position Limits: " + (m_positionLimitsOK ? "OK" : "FAILED") + "\n";
    summary += "  Daily Limits: " + (m_dailyLimitsOK ? "OK" : "FAILED") + "\n";
    summary += "  Spread Limits: " + (m_spreadOK ? "OK" : "FAILED") + "\n";
    summary += "  Last Message: " + m_lastRiskMessage;
    
    return summary;
}

//+------------------------------------------------------------------+
//| Log risk status                                                  |
//+------------------------------------------------------------------+
void RiskChecker::LogRiskStatus() const
{
    Print("RiskChecker Status:");
    Print("  Account: ", (m_accountLimitsOK ? "OK" : "FAILED"));
    Print("  Position: ", (m_positionLimitsOK ? "OK" : "FAILED"));
    Print("  Daily: ", (m_dailyLimitsOK ? "OK" : "FAILED"));
    Print("  Spread: ", (m_spreadOK ? "OK" : "FAILED"));
    if (m_lastRiskMessage != "")
        Print("  Message: ", m_lastRiskMessage);
}

//+------------------------------------------------------------------+
//| Set risk message                                                 |
//+------------------------------------------------------------------+
void RiskChecker::SetRiskMessage(string message)
{
    m_lastRiskMessage = message;
}

#endif // RISK_CHECKER_MQH
